<template>
  <div class="home">
    <div class="home-left">
      <Box
        :title="'桥梁概况'"
        class="Box1"
        :onTitleClick="
          () => {
            showModal1 = true;
          }
        "
      >
        <template v-slot:content>
          <div class="sum">
            <span
              >桥梁总数 <i>{{ BridgeData?.totalBridges || 0 }} </i></span
            >
            <span @click="flowWeather">水流气象 <i> </i></span>
          </div>
          <div class="bot">
            <div class="box">
              <span>已开通</span>
              <span>{{ BridgeData?.bridgeStateCounts.start2 || 0 }}<i class="sum1"></i></span>
            </div>
            <div class="box">
              <span>建设中</span>
              <span>{{ BridgeData?.bridgeStateCounts.start1 || 0 }}<i class="sum2"></i></span>
            </div>
            <div class="box">
              <span>规划中</span>
              <span>{{ BridgeData?.bridgeStateCounts.start0 || 0 }}<i class="sum3"></i></span>
            </div>
          </div>
        </template>
      </Box>
      <Box :title="'桥梁规模'" class="Box2">
        <template v-slot:content>
          <div class="con-1">
            <div class="img"></div>
            <div class="list">
              <span>桥梁里程</span>
              <span
                ><i>{{ ScaleData?.totalLength || 0 }}</i
                >米</span
              >
            </div>
          </div>
          <div class="bg"></div>
          <!-- <div class="con-1 con-2">
            <div class="con">
              <div class="img img1"></div>
              <div class="list">
                <span>链接岛屿</span>
                <span
                  ><i>{{ ScaleData?.totalIslands || 0 }}</i
                  >座</span
                >
              </div>
            </div>
            <div class="con">
              <div class="img img2"></div>
              <div class="list">
                <span>收费口</span>
                <span
                  ><i>{{ ScaleData?.totalTollBooths || 0 }}</i
                  >个</span
                >
              </div>
            </div>
          </div> -->
          <div class="line">
            <div class="box" v-for="(item, index) in customColors" :key="index" @click="bridgeClick(item)">
              <span :title="item.bridgeName"> {{ item.bridgeName }}</span>
              <el-progress :percentage="item.percentage" color="#1989fa" :show-text="false" />
              <span
                ><i>{{ item.bridgeLength }}</i
                >米</span
              >
            </div>
          </div>
        </template>
      </Box>
    </div>
    <div class="home-right">
      <Box :title="'通行流量'" class="Box1">
        <template v-slot:unit>
          <div class="unit">
            <el-select v-model="value" placeholder="Select" @change="updateTime" size="large" style="width: 72px">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <div class="btn1" @click="onClickFc1"></div>
            <div class="btn2" @click="onClickFc2"></div>
          </div>
        </template>
        <template v-slot:content>
          <div class="FlowEcharts" ref="FlowEchartsRef"></div>
        </template>
      </Box>
      <Box :title="'桥安路畅指数'" class="Box2">
        <template v-slot:content>
          <div class="echarts">
            <div class="echarts1" ref="aqzsEchartsRef"></div>
            <div class="echarts2" ref="ylzsEchartsRef"></div>
          </div>
        </template>
      </Box>
      <Box :title="'运行分析'" class="Box3">
        <template v-slot:content>
          <div class="name">
            <span>桥梁</span>
            <span>流量<i>车次/小时</i></span>
            <span>报警数</span>
            <!--            <span>通过时间</span>-->
            <span>畅通指数</span>
            <span>主题任务</span>
          </div>
          <div class="list">
            <div class="line" v-for="(item, index) in analyzeData" :key="index">
              <span class="line-span">{{ item.label1 }}</span>
              <div class="line2">
                <span>{{ item.label2 }}</span>
                <div class="zz">
                  <span><i class="i1">+</i>{{ item.max }}</span> <span><i class="i2">-</i>{{ item.min }}</span>
                </div>
              </div>
              <!--              <span class="line-span">{{ item.label3 }}</span>-->
              <span class="line-span">{{ item.label4 }}</span>
              <span class="line-span" :class="FlowState(item.label5)">{{ item.label5 }}</span>
              <span class="line-span"><img :src="item.img" v-if="item.img" alt="" /></span>
            </div>
          </div>
        </template>
      </Box>
    </div>
  </div>
  <!-- 桥梁概况弹窗 -->
  <PopUp :visible="showModal1" title="桥梁概况" @update:visible="showModal1 = $event" class="PopUp1">
    <div class="modal-content">
      <!-- 搜索框 -->
      <div class="btn">
        <span v-for="item in activeName" :key="item.id" :class="item.id == currentIndex ? 'current' : ''" @click="questList(item.id)">{{
          item.name
        }}</span>
      </div>
      <Table :columns="columns" :fetchData="fetchData" :searchParams="searchParams">
        <template #buildDate="scope">
          <span>{{ scope.row.buildDate ? scope.row.buildDate.split(' ')[0] : '' }}</span>
        </template>
        <!-- 自定义列内容 -->
        <template #details="scope">
          <el-button type="primary" @click="viewDetails(scope.row)" style="width: 78px; height: 32px"
            ><el-icon><Document /></el-icon>详情</el-button
          >
          <el-button type="primary" @click="viewDetails2(scope.row)"
            ><el-icon><Document /></el-icon>警戒力量</el-button
          >
        </template>
      </Table>
    </div>
  </PopUp>
  <!-- 桥梁概况详情弹窗 -->
  <PopUp :visible="showModal2" title="桥梁详情信息" @update:visible="showModal2 = $event" class="PopUp2" style="left: 20%; width: 1014px">
    <div class="modal-content">
      <div class="con-box1">
        <div class="tit">
          <span></span>
          <span>桥梁基本信息</span>
        </div>
        <div class="list">
          <img class="img" :src="BridgeInfoData.fileUrl[0]" alt="" v-if="BridgeInfoData.fileUrl[0]" />
          <noData class="img" v-else text="暂无图片"></noData>
          <div class="left-r">
            <div class="li">
              <span
                >桥梁编号：<i>{{ BridgeInfoData.id || '' }}</i></span
              >
              <span
                >桥梁结构：<i>{{ BridgeInfoData.bridgeType || '' }}</i></span
              >
            </div>
            <div class="li">
              <span
                >桥梁名称：<i>{{ BridgeInfoData.bridgeName || '' }}</i></span
              >
              <span
                >桥梁长度：<i>{{ BridgeInfoData.bridgeLength || '' }}米</i></span
              >
            </div>
            <div class="li">
              <span
                >桥梁宽度：<i>{{ BridgeInfoData.bridgeWidth || '' }}米</i></span
              >
              <span
                >桥梁高度：<i>{{ BridgeInfoData.bridgeHeight || '' }}米</i></span
              >
            </div>
            <div class="li">
              <span
                >设计载荷：<i>{{ BridgeInfoData.designLoad || '' }}吨</i></span
              >
              <span
                >所属区域：<i>{{ BridgeInfoData.region || '' }}</i></span
              >
            </div>
            <div class="li">
              <span
                >管理单位：<i>{{ BridgeInfoData.deptName || '' }}</i></span
              >
              <span
                >建造年限：<i>{{ BridgeInfoData.buildDate ? BridgeInfoData.buildDate.split(' ')[0] : '' }}</i></span
              >
            </div>
          </div>
        </div>
      </div>
      <div class="con-box2">
        <div class="tit">
          <span></span>
          <span>健康监测信息</span>
        </div>
        <div class="name">
          <span>监测编号</span>
          <span>监测时间</span>
          <span>健康评分</span>
          <span>问题</span>
          <span>监测单位</span>
        </div>
        <div class="list">
          <div class="line" v-for="(item, index) in HealthData" :key="index">
            <span class="line-span">{{ item.bridgeUniqueCode }}</span>
            <span class="line-span">{{ item.updateTime }}</span>
            <span class="line-span">{{ item.entireHealthLevel }}</span>
            <span class="line-span"></span>
            <span class="line-span"></span>
          </div>
          <noData v-if="HealthData.length == 0"></noData>
        </div>
      </div>
      <div class="con-box3">
        <div class="tit">
          <span></span>
          <span>历史事件信息</span>
        </div>
        <div class="name">
          <span>事件ID</span>
          <span>事件名称</span>
          <span>发生地点</span>
          <span>处置状态</span>
          <span>发生时间</span>
        </div>
        <div class="list">
          <div class="line" v-for="(item, index) in HistoryData" :key="index">
            <span class="line-span">{{ item.id }}</span>
            <span class="line-span">{{ item.eventName }}</span>
            <span class="line-span">{{ item.location }}</span>
            <span class="line-span">{{ item.status == 1 ? '未处理' : item.status == 2 ? '处理中' : '已处理' }}</span>
            <span class="line-span">{{ item.happenTime }}</span>
          </div>
          <noData v-if="HistoryData.length == 0"></noData>
        </div>
      </div>
      <div class="con-box3">
        <div class="tit">
          <span></span>
          <span>安全设备</span>
        </div>
        <div class="name">
          <span>设备ID</span>
          <span>设备名称</span>
          <span>安装位置</span>
          <span>设备状态</span>
          <span>监测时间</span>
        </div>
        <div class="list">
          <div class="line" v-for="(item, index) in safetyData" :key="index">
            <span class="line-span">{{ item.equipId }}</span>
            <span class="line-span">{{ item.equipName }}</span>
            <span class="line-span">{{ item.installPosition }}</span>
            <span class="line-span">{{ item.status == 0 ? '正常' : item.status == 1 ? '故障' : '维护中' }}</span>
            <span class="line-span">{{ item.lastCheckTime }}</span>
          </div>
          <noData v-if="safetyData.length == 0"></noData>
        </div>
      </div>
      <div class="con-box3">
        <div class="tit">
          <span></span>
          <span>桥梁运维</span>
        </div>
        <div class="name">
          <span>维护ID</span>
          <span>任务名称</span>
          <span>执行人</span>
          <span>任务状态</span>
          <span>执行时间</span>
        </div>
        <div class="list">
          <div class="line" v-for="(item, index) in bridgeRepairData" :key="index">
            <span class="line-span">{{ item.maintainId }}</span>
            <span class="line-span">{{ item.taskName }}</span>
            <span class="line-span">{{ item.executePerson }}</span>
            <span class="line-span">{{ item.taskStatus == 1 ? '已完成' : item.status == 2 ? '进行中' : '计划中' }}</span>
            <span class="line-span">{{ item.executeTime }}</span>
          </div>
          <noData v-if="bridgeRepairData.length == 0"></noData>
        </div>
      </div>
    </div>
  </PopUp>
  <!-- 警戒力量弹窗 -->
  <PopUp :visible="showModal3" title="警戒力量" @update:visible="showModal3 = $event" class="PopUp3" style="right: 20%; width: 1014px">
    <div class="modal-content">
      <div class="con-box2">
        <div class="tit">
          <span></span>
          <span>执法机构</span>
        </div>
        <div class="name">
          <span>执法部门</span>
          <span>部门ID</span>
          <span>部门状态</span>
          <span>上级部门ID</span>
          <span>创建时间</span>
        </div>
        <div class="list">
          <div class="line" v-for="(item, index) in enforceData" :key="index">
            <span class="line-span">{{ item.sysDeptName }}</span>
            <span class="line-span">{{ item.sysDeptId }}</span>
            <span class="line-span">{{ item.sysStatus == 0 ? '正常' : '停用' }}</span>
            <span class="line-span">{{ item.sysParentId }}</span>
            <span class="line-span">{{ item.sysCreateTime }}</span>
          </div>
          <noData v-if="enforceData.length == 0"></noData>
        </div>
      </div>
      <div class="con-box3">
        <div class="tit">
          <span></span>
          <span>工程车辆</span>
        </div>
        <div class="name">
          <span>车辆类型</span>
          <span>车辆负责人</span>
          <span>当前位置</span>
          <span>车辆状态</span>
          <span>联系电话</span>
        </div>
        <div class="list">
          <div class="line" v-for="(item, index) in engineeringData" :key="index">
            <span class="line-span">{{ item.vehicleType }}</span>
            <span class="line-span">{{ item.responsiblePerson }}</span>
            <span class="line-span">{{ item.currentPosition }}</span>
            <span class="line-span">{{ item.status == 1 ? '待命' : item.status == 2 ? '执行任务' : item.status == 3 ? '维修' : '报废' }}</span>
            <span class="line-span">{{ item.contactPhone }}</span>
          </div>
          <noData v-if="engineeringData.length == 0"></noData>
        </div>
      </div>
    </div>
  </PopUp>

  <!-- 水流气象 -->
  <PopUp :visible="showModal4" title="水流气象" @update:visible="showModal4 = $event" class="PopUp4" style="right: 20%; width: 1014px">
    <div class="modal-content">
      <div class="con-box2">
        <div class="tit">
          <span></span>
          <span>气象监测</span>
        </div>
        <div class="name">
          <span>年份</span>
          <span>台风ID</span>
          <span>台风中文名称</span>
          <span>台风英文名称</span>
          <span>是否活跃</span>
          <span>时间</span>
          <span>强度等级</span>
          <span>风力等级</span>
          <span>经度</span>
          <span>纬度</span>
          <span>中心气压(百帕)</span>
          <span>最大风速(米/秒)</span>
          <span>风向</span>
          <span>移动速度(公里/时)</span>
          <span>移动方向</span>
        </div>
        <div class="list">
          <div class="line" v-for="(item, index) in weatherData" :key="index">
            <span class="line-span">{{ item.year || '-' }}</span>
            <span class="line-span">{{ item.typhoonId || '-' }}</span>
            <span class="line-span">{{ item.typhoonNameCn || '-' }}</span>
            <span class="line-span">{{ item.typhoonNameEn || '-' }}</span>
            <span class="line-span">{{ item.isCurrent == 1 ? '是' : '否' }}</span>
            <span class="line-span">{{ item.time || '-' }}</span>
            <span class="line-span">{{ item.strength || '-' }}</span>
            <span class="line-span">{{ item.power || '-' }}</span>
            <span class="line-span">{{ item.longitude || '-' }}</span>
            <span class="line-span">{{ item.latitude || '-' }}</span>
            <span class="line-span">{{ item.pressure || '-' }}</span>
            <span class="line-span">{{ item.windSpeed || '-' }}</span>
            <span class="line-span">{{ item.windDirection || '-' }}</span>
            <span class="line-span">{{ item.moveSpeed || '-' }}</span>
            <span class="line-span">{{ item.moveDirection || '-' }}</span>
          </div>
          <noData v-if="weatherData.length == 0"></noData>
        </div>

        <!-- 风圈半径信息 -->
        <div class="tit" style="margin-top: 20px;">
          <span></span>
          <span>风圈半径信息</span>
        </div>
        <div class="name">
          <span>7级风圈-东北(公里)</span>
          <span>7级风圈-东南(公里)</span>
          <span>7级风圈-西南(公里)</span>
          <span>7级风圈-西北(公里)</span>
          <span>10级风圈-东北(公里)</span>
          <span>10级风圈-东南(公里)</span>
          <span>10级风圈-西南(公里)</span>
          <span>10级风圈-西北(公里)</span>
          <span>12级风圈-东北(公里)</span>
          <span>12级风圈-东南(公里)</span>
          <span>12级风圈-西南(公里)</span>
          <span>12级风圈-西北(公里)</span>
        </div>
        <div class="list">
          <div class="line" v-for="(item, index) in weatherData" :key="index">
            <span class="line-span">{{ item.radius7Ne || '-' }}</span>
            <span class="line-span">{{ item.radius7Se || '-' }}</span>
            <span class="line-span">{{ item.radius7Sw || '-' }}</span>
            <span class="line-span">{{ item.radius7Nw || '-' }}</span>
            <span class="line-span">{{ item.radius10Ne || '-' }}</span>
            <span class="line-span">{{ item.radius10Se || '-' }}</span>
            <span class="line-span">{{ item.radius10Sw || '-' }}</span>
            <span class="line-span">{{ item.radius10Nw || '-' }}</span>
            <span class="line-span">{{ item.radius12Ne || '-' }}</span>
            <span class="line-span">{{ item.radius12Se || '-' }}</span>
            <span class="line-span">{{ item.radius12Sw || '-' }}</span>
            <span class="line-span">{{ item.radius12Nw || '-' }}</span>
          </div>
          <noData v-if="weatherData.length == 0"></noData>
        </div>
      </div>
      <div class="con-box3">
          <div class="tit">
            <span></span>
            <span>水域监测</span>
          </div>
          <div class="name">
            <span>历史日期</span>
            <span>水位测量值(米)</span>
            <span>测量时间戳</span>
            <span>监测站名称</span>
            <span>经度</span>
            <span>纬度</span>
          </div>
          <div class="list">
            <div class="line" v-for="(item, index) in flowData" :key="index">
              <span class="line-span">{{ item.historyDate }}</span>
              <span class="line-span">{{ item.waterLevelValue }}</span>
              <span class="line-span">{{ item.measurementTimeUnix }}</span>
              <span class="line-span">{{ item.stationName }}</span>
              <span class="line-span">{{ item.longitude }}</span>
              <span class="line-span">{{ item.latitude }}</span>
            </div>
            <noData v-if="flowData.length == 0"></noData>
          </div>
          <!-- 分页组件 -->
          <div class="pagination" style="margin-top: 20px; text-align: center;">
            <CustomPagination
              v-model:page="currentPage"
              v-model:limit="pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="total"
              @pagination="handlePaginationChange"
            />
          </div>
        </div>
    </div>
  </PopUp>

  <!-- 感知设备弹窗 -->
  <PopUp :visible="isShow" title="感知设备" @update:visible="isShow = $event" class="PopUp5">
    <div class="modal-content">
      <div class="line">
        <div class="line-box">
          <div class="dot dot1"></div>
          AIS
        </div>
        <div class="line-box">
          <div class="dot dot2"></div>
          雷达
        </div>
      </div>
      <div class="line">
        <div class="line-box">
          <div class="dot dot3"></div>
          气象
        </div>
        <div class="line-box">
          <div class="dot dot4"></div>
          隧道
        </div>
      </div>
      <div class="line">
        <div class="line-box">
          <div class="dot dot5"></div>
          监控
        </div>
        <div class="line-box">
          <div class="dot dot6"></div>
          卡口
        </div>
      </div>
      <div class="line">
        <div class="line-box" style="width: 100%">
          <div class="dot dot7"></div>
          大桥健康监测传感器
        </div>
      </div>
    </div>
  </PopUp>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import Box from '@/components/Box/index.vue';
import PopUp from '@/components/PopUp/index.vue';
import CustomPagination from '@/components/CustomPagination/index.vue';
import * as echarts from 'echarts';
import sgImg from '@/assets/home/<USER>';
import jtsgImg from '@/assets/home/<USER>';
import whpImg from '@/assets/home/<USER>';
import {
  setViewToCoordinates,
  addPoint,
  clearTypeFeatures,
  getAllPoints,
  getPointById,
  updatePointCoordinates,
  addBridge,
  focusOnBridge
} from '@/utils/mapMethods';
import cheImg from '@/assets/home/<USER>';
import {
  getBridgeSum,
  getBridgeScale,
  getBridgeLengths,
  getBridgeState,
  getBridgeIdHealth,
  getBridgeSafetyEquip,
  getListByBridgeId,
  getTrafficEngineeringVehicle,
  getWithDeptId,
  getByType,
  getWaterMonitoring,
  getMeteorologicalMonitoring,
  getCheckpointDevice,
  getSensingData
} from '@/api/bridge/home';
import { getTrafficFlowStats, getDirectionFlowStats, getTrafficFlowAnalysis } from '@/api/bridge/trafficFlow';
import noData from '@/components/noData/index.vue';
import aisImg from '@/assets/point/ais.png';
import jkImg from '@/assets/point/jk.png';
import sdImg from '@/assets/point/sd.png';
import kkImg from '@/assets/point/kk.png';
import ldsbImg from '@/assets/point/ldsb.png';
import qxImg from '@/assets/point/qx.png';
import dqjkImg from '@/assets/point/dqjk.png';

const analyzeData = ref([
  {
    label1: '金塘大桥',
    label2: '3200',
    max: '1800',
    min: '1400',
    label3: '3',
    label4: '45min',
    label5: '大流量',
    img: sgImg
  },
  {
    label1: '金塘大桥',
    label2: '3200',
    max: '1800',
    min: '1400',
    label3: '3',
    label4: '45min',
    label5: '拥堵',
    img: jtsgImg
  },
  {
    label1: '金塘大桥',
    label2: '3200',
    max: '1800',
    min: '1400',
    label3: '3',
    label4: '45min',
    label5: '流畅',
    img: whpImg
  },
  {
    label1: '金塘大桥',
    label2: '3200',
    max: '1800',
    min: '1400',
    label3: '3',
    label4: '45min',
    label5: '大流量',
    img: ''
  },
  {
    label1: '金塘大桥',
    label2: '3200',
    max: '1800',
    min: '1400',
    label3: '3',
    label4: '45min',
    label5: '大流量',
    img: ''
  },
  {
    label1: '金塘大桥',
    label2: '3200',
    max: '1800',
    min: '1400',
    label3: '3',
    label4: '45min',
    label5: '大流量',
    img: ''
  },
  {
    label1: '金塘大桥',
    label2: '3200',
    max: '1800',
    min: '1400',
    label3: '3',
    label4: '45min',
    label5: '大流量',
    img: ''
  },
  {
    label1: '金塘大桥',
    label2: '3200',
    max: '1800',
    min: '1400',
    label3: '3',
    label4: '45min',
    label5: '大流量',
    img: ''
  }
]);
//用来判断通畅指数的状态
const FlowState = (item) => {
  if (item == '大流量') {
    return 'line5-dll';
  } else if (item == '拥堵') {
    return 'line5-yd';
  } else {
    return 'line5-lc';
  }
};
// 图表引用
const FlowEchartsRef = ref<HTMLElement | null>(null);
const aqzsEchartsRef = ref<HTMLElement | null>(null);
const ylzsEchartsRef = ref<HTMLElement | null>(null);

// 图表实例
let FlowEcharts: echarts.ECharts | null = null;
let aqzsEcharts: echarts.ECharts | null = null;
let ylzsEcharts: echarts.ECharts | null = null;
// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  FlowEcharts?.resize();
  aqzsEcharts?.resize();
  ylzsEcharts?.resize();
};

//水流气象
const showModal4 = ref(false);
const weatherData = ref([]); //气象监测数据
const flowData = ref([]); //水流数据
const currentPage = ref(1); //当前页码
const pageSize = ref(10); //每页条数
const total = ref(0); //总条数

//分页大小改变
const handleSizeChange = (size) => {
  pageSize.value = size;
  getWaterMonitoringData();
};

// 处理分页变化
const handlePaginationChange = ({ page, limit }) => {
  currentPage.value = page;
  pageSize.value = limit;
  getWaterMonitoringData();
};

//获取水域监测数据
const getWaterMonitoringData = async () => {
  const res = await getWaterMonitoring({
    pageNum: currentPage.value,
    pageSize: pageSize.value
  });
  if (res.code == 200) {
    flowData.value = res.rows;
    total.value = res.total || 0;
  }
};

const flowWeather = async () => {
  showModal4.value = true;
  await getWaterMonitoringData();
  const res2 = await getMeteorologicalMonitoring({    pageNum: currentPage.value,
    pageSize: pageSize.value});
  if (res2.code == 200) weatherData.value = res2.rows;
};

//警戒力量弹窗
const showModal3 = ref(false); //警戒力量
const engineeringData = ref([]); //工程车辆
const enforceData = ref([]); //执法机构

//警戒力量入口
const viewDetails2 = async (data) => {
  showModal3.value = true;
  const res = await getTrafficEngineeringVehicle({ bridgeId: data.id }); //工程车辆数据
  const res2 = await getWithDeptId({ bridgeId: data.id }); //执法机构
  if (res.code == 200) {
    engineeringData.value = res.data;
  }
  if (res2.code == 200) {
    enforceData.value = res2.data;
  }
};

//桥梁概况
const showModal1 = ref(false); // 桥梁概况弹窗
const showModal2 = ref(false); // 桥梁详情弹窗
const BridgeData = ref<any>();
//获取桥梁数量接口
const getBridgeSumFc = async () => {
  const res = await getBridgeSum({});
  if (res.code == 200) {
    BridgeData.value = res.data;
    // console.log(res);
  }
};
const activeName = ref([
  {
    name: '已开通',
    id: 2
  },
  {
    name: '建设中',
    id: 1
  },
  {
    name: '规划中',
    id: 0
  }
]);
//条件查询 2 已开通 1 建设中 0 规划中
const searchParams = ref({
  bridgeState: '2'
});
const currentIndex = ref(2);
//条件查询
const questList = (index) => {
  currentIndex.value = index;
  searchParams.value.bridgeState = index;
};

const ScaleData = ref<any>();
//获取桥梁规模
const getBridgeScaleFc = async () => {
  const res = await getBridgeScale({});
  if (res.code == 200) {
    ScaleData.value = res.data;
    // console.log(res);
  }
};
//点击桥定位视角过去
const bridgeClick = (item) => {
  // console.log(item);
  if (item.lon && item.lat) {
    setViewToCoordinates([item.lon, item.lat]);
  }
};
const customColors = ref([
  { color: '#f56c6c', bridgeLength: 0, bridgeName: '舟岱大桥', percentage: 0 },
  { color: '#e6a23c', bridgeLength: 0, bridgeName: '金塘大桥', percentage: 0 },
  { color: '#5cb87a', bridgeLength: 0, bridgeName: '鱼山大桥', percentage: 0 },
  { color: '#1989fa', bridgeLength: 0, bridgeName: '西堠门大桥', percentage: 0 },
  { color: '#1989fa', bridgeLength: 0, bridgeName: '西堠门大桥', percentage: 0 },
  { color: '#1989fa', bridgeLength: 0, bridgeName: '西堠门大桥', percentage: 0 }
]);
//获取桥梁长度
const getBridgeLengthsFc = async () => {
  const res = await getBridgeLengths({});
  if (res.code == 200) {
    let maxLength = res.data[0].bridgeLength; //根据最大的桥梁长度比
    customColors.value = res.data.map((item) => {
      return {
        percentage: ((item.bridgeLength / maxLength) * 100).toFixed(2),
        ...item
      };
    });
    // console.log(customColors.value);
  }
};

const columns = ref([
  {
    'prop': 'id',
    'label': '桥梁编号',
    'align': 'center'
  },
  {
    'prop': 'bridgeName',
    'label': '桥梁名称',
    'align': 'center'
  },
  {
    'prop': 'deptName',
    'label': '管理单位',
    'align': 'center'
  },
  {
    'prop': 'region',
    'label': '桥梁位置',
    'align': 'center'
  },
  {
    'prop': 'bridgeType',
    'label': '桥梁结构',
    'align': 'center',
    'sortable': true // 支持排序
  },
  {
    'prop': 'bridgeLength',
    'label': '桥梁长度(米)',
    'align': 'center',
    'show-overflow-tooltip': true // 内容过长时显示提示框
  },
  {
    'prop': 'bridgeWidth',
    'label': '桥梁宽度(米)',
    'align': 'center',
    'show-overflow-tooltip': true
  },
  {
    'prop': 'buildDate',
    'label': '建造年限',
    'align': 'center',
    'sortable': true, // 支持按时间排序
    'slot': 'buildDate'
  },
  {
    'prop': 'details',
    'label': '操作',
    'slot': 'details', // 使用插槽自定义操作按钮（如“详情”链接）
    'width': '300'
  }
]);
//桥梁概况基本数据
const fetchData = async (params: {}) => {
  try {
    const res = await getBridgeState(params);
    if (res.code == 200) {
      return {
        data: res.data.records || [],
        total: res.data.total || 0
      };
    }
    // console.log(res);
  } catch (error) {
    console.error('获取数据失败:', error);
    ElMessage.error('获取数据失败');
    return {
      data: [],
      total: 0
    };
  }
};
//桥梁基本信息数据
const BridgeInfoData = ref<any>();
//桥梁健康监测信息数据
const HealthData = ref([]);
//桥梁历史事件信息数据
const HistoryData = ref([]);
//安全设备数据
const safetyData = ref([]);
//桥梁运维
const bridgeRepairData = ref([]);
//查看详情
const viewDetails = async (item) => {
  showModal2.value = true;
  BridgeInfoData.value = {
    ...item,
    fileUrl: JSON.parse(item.fileUrl)
  };
  const res = await getBridgeIdHealth({ id: item.id, bridgeName: item.bridgeName });
  const res2 = await getBridgeSafetyEquip(item.id); //桥梁安全设备
  const res3 = await getListByBridgeId(item.id); //桥梁运维
  if (res2.code == 200) {
    safetyData.value = res2.data;
  }
  if (res3.code == 200) {
    bridgeRepairData.value = res3.data;
  }
  if (res.code == 200) {
    HealthData.value = res.data.healthInfo;
    HistoryData.value = res.data.alarmInfo;
    // console.log(res);
  }
  // console.log('查看详情', item);
};

//通行流量点击事件
//船流量
const onClickFc1 = () => {
  type.value = 1;
  // initFlowEcharts({ type: 1, time: value.value });
};

const value: any = ref(1);
const type = ref(1);
const options = [
  {
    value: 1,
    label: '日'
  },
  {
    value: 2,
    label: '月'
  },
  {
    value: 3,
    label: '年'
  }
];
//更新时间
const updateTime = (value) => {
  initFlowEcharts();
};
//车流量
const onClickFc2 = () => {
  type.value = 2;
  // initFlowEcharts({ type: 2, time: value.value });
};
//通行流量图表
const initFlowEcharts = async () => {
  const xData = [];
  const inData = [];
  const outData = [];
  const historyData = [];

  try {
    // 使用新的流量统计接口
    const res: any = await getTrafficFlowStats(value.value);
    if (res.code === 200) {
      Object.keys(res.data).forEach((key) => {
        xData.push(key);
        inData.push(res.data[key].value1);
        outData.push(res.data[key].value2);
        historyData.push(res.data[key].value3);
      });
    } else {
      console.error('流量统计接口返回错误:', res.msg);
    }
  } catch (error) {
    console.error('获取流量趋势数据失败:', error);
  }

  if (FlowEchartsRef.value) {
    FlowEcharts = echarts.init(FlowEchartsRef.value);
    const option = {
      xAxis: {
        type: 'category',
        data: xData,
        axisLabel: {
          color: '#86868d',
          fontSize: 20
        },
        axisLine: {
          lineStyle: {
            color: '#86868d'
          }
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#86868d',
          fontSize: 20
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#86868d'
          }
        },
        splitLine: {
          show: false // 不显示 Y 轴的网格线
        }
      },
      series: [
        {
          name: '进',
          data: inData,
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#a6d8fc',
            width: 2
          },
          areaStyle: {
            color: 'rgba(168, 214, 253, 0.2)' // 设置面积颜色
          },
          itemStyle: {
            color: '#a6d8fc'
          }
        },
        {
          name: '出',
          data: outData,
          type: 'line',
          smooth: true,
          areaStyle: {
            color: 'rgba(42, 191, 174, 0.2)' // 设置面积颜色
          },
          lineStyle: {
            color: '#35bfb0',
            width: 2
          },
          itemStyle: {
            color: '#35bfb0'
          }
        },
        {
          name: '历史',
          data: historyData,
          type: 'line',
          smooth: true,
          areaStyle: {
            color: 'rgba(96, 129, 237, 0.2)' // 设置面积颜色
          },
          lineStyle: {
            color: '#6081ed',
            width: 2
          },
          itemStyle: {
            color: '#6081ed'
          }
        }
      ],
      legend: {
        right: 60,
        data: ['进', '出', '历史'],
        textStyle: {
          color: '#fff',
          fontSize: 20
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#23334e', // 背景色
        textStyle: {
          color: '#fff' // 字体颜色
        }
      },
      grid: {
        top: 40,
        right: 0,
        bottom: 0,
        left: 0,
        containLabel: true,
        show: true,
        borderWidth: 0 // 不显示外边框
      }
    };
    FlowEcharts.setOption(option);
  }
};
//安全指数图表
const initaqzsEcharts = () => {
  if (aqzsEchartsRef.value) {
    aqzsEcharts = echarts.init(aqzsEchartsRef.value);
    const option = {
      series: [
        {
          type: 'gauge',
          startAngle: 180,
          endAngle: 0,
          center: ['50%', '100%'],
          radius: '200%',
          min: 0,
          max: 1,
          splitNumber: 8,
          axisLine: {
            lineStyle: {
              width: 6,
              color: [
                [0.25, '#FF6E76'],
                [0.5, '#FDDD60'],
                [0.75, '#58D9F9'],
                [1, '#7CFFB2']
              ]
            }
          },
          pointer: {
            icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
            length: '12%',
            width: 20,
            offsetCenter: [0, '-60%'],
            itemStyle: {
              color: 'auto'
            }
          },
          axisTick: {
            length: 12,
            lineStyle: {
              color: 'auto',
              width: 2
            }
          },
          splitLine: {
            length: 20,
            lineStyle: {
              color: 'auto',
              width: 5
            }
          },
          axisLabel: {
            show: false // 去除仪表盘外面的数字
          },
          title: {
            offsetCenter: [0, '-10%'],
            fontSize: 20,
            color: '#fff'
          },
          detail: {
            fontSize: 30,
            offsetCenter: [0, '-35%'],
            valueAnimation: true,
            formatter: function (value) {
              return Math.round(value * 100) + '%';
            },
            color: 'inherit'
          },
          data: [
            {
              value: 0.85,
              name: '安全指数'
            }
          ]
        },
        {
          type: 'gauge',
          startAngle: 180,
          endAngle: 0,
          center: ['50%', '75%'],
          radius: '100%', // 外圈背景的半径
          min: 0,
          max: 1,
          splitNumber: 8,
          axisLine: {
            lineStyle: {
              width: 6,
              color: '#eee' // 外圈背景颜色
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          pointer: {
            show: false
          },
          detail: {
            show: false
          }
        }
      ]
    };
    aqzsEcharts.setOption(option);
  }
};
//压力指数图标
const initylzsEcharts = () => {
  if (ylzsEchartsRef.value) {
    ylzsEcharts = echarts.init(ylzsEchartsRef.value);
    const option = {
      series: [
        {
          type: 'gauge',
          startAngle: 180,
          endAngle: 0,
          center: ['50%', '100%'],
          radius: '200%',
          min: 0,
          max: 1,
          splitNumber: 8,
          axisLine: {
            lineStyle: {
              width: 6,
              color: [
                [0.25, '#FF6E76'],
                [0.5, '#FDDD60'],
                [0.75, '#58D9F9'],
                [1, '#7CFFB2']
              ]
            }
          },
          pointer: {
            icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
            length: '12%',
            width: 20,
            offsetCenter: [0, '-60%'],
            itemStyle: {
              color: 'auto'
            }
          },
          axisTick: {
            length: 12,
            lineStyle: {
              color: 'auto',
              width: 2
            }
          },
          splitLine: {
            length: 20,
            lineStyle: {
              color: 'auto',
              width: 5
            }
          },
          axisLabel: {
            show: false // 去除仪表盘外面的数字
          },
          title: {
            offsetCenter: [0, '-10%'],
            fontSize: 20,
            color: '#fff'
          },
          detail: {
            fontSize: 30,
            offsetCenter: [0, '-35%'],
            valueAnimation: true,
            formatter: function (value) {
              return Math.round(value * 100) + '%';
            },
            color: 'inherit'
          },
          data: [
            {
              value: 0.75,
              name: '压力指数'
            }
          ]
        },
        {
          type: 'gauge',
          startAngle: 180,
          endAngle: 0,
          center: ['50%', '75%'],
          radius: '100%', // 外圈背景的半径
          min: 0,
          max: 1,
          splitNumber: 8,
          axisLine: {
            lineStyle: {
              width: 6,
              color: '#eee' // 外圈背景颜色
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          pointer: {
            show: false
          },
          detail: {
            show: false
          }
        }
      ]
    };
    ylzsEcharts.setOption(option);
  }
};
//获取运行分析数据
const getAnalyzeData = async () => {
  try {
    const res = await getTrafficFlowAnalysis();
    if (res.code === 200 && res.data.length > 0) {
      // 将接口数据转换为组件需要的格式
      const newAnalyzeData = res.data.slice(0, 6).map((item, index) => ({
        label1: item.hubCode01 + '-' + item.hubCode02, // 断面名称
        label2: item.vehicleCls.toString(), // 流量
        max: Math.floor(item.vehicleCls * 1.2).toString(), // 最大值
        min: Math.floor(item.vehicleCls * 0.8).toString(), // 最小值
        label3: ' ', // 报警数（接口中没有，暂时设为0）
        label4: ' ', // 通过时间（接口中没有，使用默认值）
        label5: item.vehicleCls > 30000 ? '大流量' : item.vehicleCls > 20000 ? '拥堵' : '流畅', // 根据流量判断状态
        img: item.vehicleCls > 30000 ? sgImg : item.vehicleCls > 20000 ? jtsgImg : whpImg
      }));
      analyzeData.value = newAnalyzeData;
    }
  } catch (error) {
    console.error('获取运行分析数据失败:', error);
  }
};

//获取方向流量数据并更新指数
const getDirectionFlowData = async () => {
  try {
    const res = await getDirectionFlowStats();
    if (res.code === 200) {
      const data = res.data;
      // 根据流量数据计算安全指数和压力指数
      const totalFlow = data.totalCount;
      const safetyIndex = Math.min(0.95, Math.max(0.3, 1 - totalFlow / 100000)); // 基于总流量计算安全指数
      const pressureIndex = Math.min(0.95, Math.max(0.3, totalFlow / 80000)); // 基于总流量计算压力指数

      // 更新安全指数图表
      updateSafetyIndex(safetyIndex);
      // 更新压力指数图表
      updatePressureIndex(pressureIndex);
    }
  } catch (error) {
    console.error('获取方向流量数据失败:', error);
  }
};

//更新安全指数
const updateSafetyIndex = (value: number) => {
  if (aqzsEcharts) {
    aqzsEcharts.setOption({
      series: [
        {
          data: [{ value: value, name: '安全指数' }]
        }
      ]
    });
  }
};

//更新压力指数
const updatePressureIndex = (value: number) => {
  if (ylzsEcharts) {
    ylzsEcharts.setOption({
      series: [
        {
          data: [{ value: value, name: '压力指数' }]
        }
      ]
    });
  }
};

onMounted(() => {
  initFlowEcharts();
  initaqzsEcharts();
  initylzsEcharts();
  getBridgeSumFc();
  getBridgeScaleFc();
  getBridgeLengthsFc();
  getAnalyzeData(); // 获取运行分析数据
  getDirectionFlowData(); // 获取方向流量数据
  window.addEventListener('resize', handleResize);
});

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  // 销毁图表实例
  FlowEcharts?.dispose();
  FlowEcharts = null;
  aqzsEcharts?.dispose();
  aqzsEcharts = null;
  ylzsEcharts?.dispose();
  ylzsEcharts = null;
});
</script>

<style scoped lang="scss">
.home {
  height: 1377px;
  width: 1442px;
  position: absolute;
  bottom: 44px;
  right: 60px;
  display: flex;
  justify-content: space-between;
  z-index: 1;
  .home-left {
    height: 1377px;
    width: 48%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .Box1 {
      height: 566px;
      width: 100%;
      .sum {
        width: 605px;
        height: 300px;
        background: url('@/assets/home/<USER>') no-repeat;
        background-size: 100%;
        margin-top: 17px;
        display: flex;
        flex-direction: column;
        span {
          color: #fff;
          font-family: 'Microsoft YaHei';
          font-size: var(--font-size-content); // 使用统一变量 25px
          i {
            font-style: normal;
            color: #fff;
            font-size: var(--font-size-data-large);
            font-weight: bold;
          }
        }
        span:nth-child(2) {
          cursor: pointer;
          color: #416194;
        }
        span:nth-child(3) {
          cursor: pointer;
          color: #416194;
        }
      }
      .bot {
        height: 140px;
        width: 605px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .box {
          width: 180px;
          height: 100%;
          color: #fff;
          font-family: 'Microsoft YaHei';
          display: flex;
          flex-direction: column;
          justify-content: center;
          padding-left: 30px;
          background: #21252d;
          span:nth-child(1) {
            font-size: var(--font-size-content); // 使用统一变量 25px
          }
          span:nth-child(2) {
            font-size: var(--font-size-data-large);
            font-weight: bold;
            display: flex;
            align-items: center;
          }
          i {
            font-style: normal;
            display: inline-block;
            width: 48px;
            height: 48px;
            margin-left: 60px;
          }
          .sum1 {
            background: url('@/assets/home/<USER>') no-repeat center;
            background-size: 100%;
          }
          .sum2 {
            background: url('@/assets/home/<USER>') no-repeat center;
            background-size: 100%;
          }
          .sum3 {
            background: url('@/assets/home/<USER>') no-repeat center;
            background-size: 100%;
          }
        }
      }
    }
    .Box2 {
      height: 774px;
      width: 100%;
      position: relative;
      .bg {
        position: absolute;
        width: 203px;
        height: 254px;
        right: 60px;
        background: url('@/assets/home/<USER>') no-repeat;
      }
      .con-1 {
        width: 605px;
        height: 140px;
        margin-top: 35px;
        padding-left: 20px;
        display: flex;
        align-items: center;
        background: #232531;
        .img {
          width: 114px;
          height: 114px;
          background: url('@/assets/home/<USER>') no-repeat;
          background-size: 100% 100%;
        }
        .img1 {
          width: 114px;
          height: 114px;
          background: url('@/assets/home/<USER>') no-repeat;
          background-size: 100% 100%;
        }
        .img2 {
          width: 114px;
          height: 114px;
          background: url('@/assets/home/<USER>') no-repeat;
          background-size: 100% 100%;
        }
        .list {
          color: #fff;
          display: flex;
          flex-direction: column;
          font-family: 'Microsoft YaHei';
          margin-left: 25px;
          span:nth-child(1) {
            font-size: var(--font-size-content); // 使用统一变量 25px
            margin-bottom: 10px;
          }
          span:nth-child(2) {
            font-size: var(--font-size-content); // 使用统一变量 25px
            i {
              font-style: normal;
              font-size: var(--font-size-data-large);
              font-weight: bold;
              margin-right: 10px;
            }
          }
        }
        .con {
          width: 49%;
          height: 100%;
          display: flex;
          align-items: center;
        }
      }
      .con-2 {
        justify-content: space-between;
      }
      .line {
        flex: 1;
        overflow-y: scroll;
        margin-top: 20px;
        padding-right: 20px;
        .box {
          height: 70px;
          width: 100%;
          margin-bottom: 20px;
          display: flex;
          justify-content: space-around;
          align-items: center;
          background: #232531;
          border-radius: 4px;
          span:nth-child(1) {
            display: inline-block;
            width: 104px;
            font-size: var(--font-size-content); // 使用统一变量 25px
            color: #9d9ea0;
            font-family: 'Microsoft YaHei';
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          span:last-child {
            font-size: var(--font-size-content); // 使用统一变量 25px
            font-family: 'Microsoft YaHei';
            color: #9d9ea0;
            i {
              font-style: normal;
              color: #fff;
              font-size: var(--font-size-content); // 使用统一变量 25px
              font-weight: bold;
            }
          }
          .el-progress {
            width: 315px;
            height: 12px;
          }
        }
      }
      ::-webkit-scrollbar-track {
        background-color: #0d0d16;
        border-radius: 20px;
      }

      /* 自定义滚动条宽度和高度 */
      ::-webkit-scrollbar {
        width: 0px;
        height: 0px;
      }
    }
  }
  .home-right {
    width: 48%;
    height: 1377px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .Box1 {
      height: 420px;
      width: 100%;
      position: relative;
      .unit {
        position: absolute;
        top: 24px;
        right: 0;
        width: 70%;
        height: 42px;
        display: flex;
        align-items: center;
        .el-select {
          background: #35373c;
          .el-select--large .el-select__wrapper {
            background: #35373c;
          }
          ::v-deep span {
            color: #fff;
            font-family: 'Microsoft YaHei';
            font-size: var(--font-size-content);
            font-weight: bold;
          }
        }
        .btn1 {
          cursor: pointer;
          width: 42px;
          height: 42px;
          background: url('@/assets/home/<USER>') no-repeat;
          background-size: 100% 100%;
          margin-left: 20px;
        }
        .btn2 {
          cursor: pointer;
          width: 42px;
          height: 42px;
          background: url('@/assets/home/<USER>') no-repeat;
          background-size: 100% 100%;
          margin-left: 10px;
        }
      }
      .FlowEcharts {
        flex: 1;
        width: 100%;
      }
    }
    .Box2 {
      height: 230px;
      width: 100%;
      position: relative;
      .echarts {
        flex: 1;
        width: 100%;
        position: relative;
        display: flex;
        justify-content: space-between;
        .echarts1 {
          width: 48%;
          height: 100%;
        }
        .echarts2 {
          width: 48%;
          height: 100%;
        }
      }
    }
    .Box3 {
      height: 670px;
      width: 100%;
      position: relative;
      .name {
        background: #262c3f;
        width: 100%;
        height: 103px;
        display: flex;
        color: #fff;
        margin-top: 20px;
        span {
          font-size: var(--font-size-content); // 使用统一变量 25px
          font-weight: bold;
          font-family: 'Microsoft YaHei';
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          min-width: 100px;
          i {
            font-style: normal;
            font-size: var(--font-size-content); // 使用统一变量 25px
            color: #91949c;
          }
        }
      }
      .list {
        width: 100%;
        height: calc(100% - 123px);
        margin-top: 10px;
        overflow-y: scroll;
        .line {
          display: flex;
          margin-top: 25px;
          .line-span {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #fff;
            font-size: var(--font-size-content); // 使用统一变量 25px
            font-family: 'Microsoft YaHei';
            height: 44px;
            width: 100px;
            img {
              width: 36px;
              height: 29px;
            }
          }
        }
        .line2 {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 100px;
          span {
            color: #fff;
            font-size: var(--font-size-content); // 使用统一变量 25px
            font-family: 'Microsoft YaHei';
          }
          .zz {
            display: flex;
            justify-content: space-between;
            width: 100%;
            span {
              color: #fff;
              font-size: var(--font-size-content); // 使用统一变量 25px
              font-family: 'Microsoft YaHei';
              i {
                font-style: normal;
              }
              .i1 {
                color: red;
              }
              .i2 {
                color: green;
              }
            }
          }
        }
        .line5-dll {
          background: #fe7c2b;
        }
        .line5-lc {
          background: #00ac28;
        }
        .line5-yd {
          background: #fe0020;
        }
      }
      /* 隐藏垂直滚动条 */
      .list::-webkit-scrollbar {
        width: 0;
      }
    }
  }
}
.PopUp1 {
  width: 40% !important;
  .btn {
    height: 36px;
    width: 100%;
    display: flex;
    padding: 0 20px;
    span {
      display: inline-block;
      width: 165px;
      height: 100%;
      border-radius: 6px;
      color: #b6b6bb;
      font-size: var(--font-size-content);
      font-family: 'Microsoft YaHei';
      background: #50505a;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 20px;
    }
    .current {
      background: #416194;
      color: #fff;
    }
  }
}
.PopUp2 {
  height: 100%;
  .modal-content {
    overflow-y: scroll;
    height: 100%;
    .con-box1 {
      margin-top: 36px;
      .tit {
        span {
          font-size: var(--font-size-module-title); // 使用模块标题变量 30px
          font-family: 'Microsoft YaHei';
          color: #fff;
        }
        span:nth-child(1) {
          display: inline-block;
          width: 43px;
          height: 22px;
          background: url('@/assets/home/<USER>') no-repeat center;
          background-size: 100% 100%;
          margin-right: 12px;
        }
      }
      .list {
        height: 150px;
        width: 100%;
        margin-top: 32px;
        display: flex;
        .img {
          width: 238px;
          height: 100%;
          // background: url('@/assets/home/<USER>') no-repeat center;
          // background-size: 100% 100%;
        }
        .left-r {
          width: calc(100% - 288px);
          margin-left: 50px;
          height: 100%;
          display: flex;
          flex-wrap: wrap;
          .li {
            width: 100%;
            span {
              display: inline-block;
              width: 50%;
              font-size: var(--font-size-content); // 使用统一变量 25px
              font-family: 'Microsoft YaHei';
              color: #fff;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            i {
              font-style: normal;
              color: #accbff;
            }
          }
        }
      }
    }
    .con-box2 {
      margin-top: 36px;
      .tit {
        span {
          font-size: var(--font-size-module-title); // 使用模块标题变量 30px
          font-family: 'Microsoft YaHei';
          color: #fff;
        }
        span:nth-child(1) {
          display: inline-block;
          width: 43px;
          height: 22px;
          background: url('@/assets/home/<USER>') no-repeat center;
          background-size: 100% 100%;
          margin-right: 12px;
        }
      }
    }
    .con-box3 {
      margin-top: 36px;
      .tit {
        span {
          font-size: var(--font-size-module-title); // 使用模块标题变量 30px
          font-family: 'Microsoft YaHei';
          color: #fff;
        }
        span:nth-child(1) {
          display: inline-block;
          width: 43px;
          height: 22px;
          background: url('@/assets/home/<USER>') no-repeat center;
          background-size: 100% 100%;
          margin-right: 12px;
        }
      }
    }
    .con-box2 {
      .name {
        background: #262c3f;
        width: 100%;
        height: 62px;
        display: flex;
        color: #fff;
        margin-top: 20px;
        span {
          font-size: var(--font-size-content); // 使用统一变量 25px
          font-weight: bold;
          font-family: 'Microsoft YaHei';
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 20%;
        }
      }
      .list {
        width: 100%;
        height: 200px;
        margin-top: 10px;
        overflow-y: scroll;
        .line {
          display: flex;
          width: 100%;
          height: 62px;

          .line-span {
            color: #fff;
            font-size: var(--font-size-content); // 使用统一变量 25px
            font-family: 'Microsoft YaHei';
            height: 100%;
            width: 20%;
            line-height: 62px;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
    .con-box3 {
      .name {
        background: #262c3f;
        width: 100%;
        height: 62px;
        display: flex;
        color: #fff;
        margin-top: 20px;
        span {
          font-size: var(--font-size-content); // 使用统一变量 25px
          font-weight: bold;
          font-family: 'Microsoft YaHei';
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: calc(100% / 6);
        }
      }
      .list {
        width: 100%;
        height: 200px;
        margin-top: 10px;
        overflow-y: scroll;
        .line {
          display: flex;
          width: 100%;
          height: 62px;

          .line-span {
            color: #fff;
            font-size: var(--font-size-content); // 使用统一变量 25px
            font-family: 'Microsoft YaHei';
            height: 100%;
            width: calc(100% / 6);
            line-height: 62px;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
      /* 隐藏垂直滚动条 */
      .list::-webkit-scrollbar {
        width: 0;
      }
    }
  }
  /* 隐藏垂直滚动条 */
  .modal-content::-webkit-scrollbar {
    width: 0;
  }
}
.PopUp3 {
  .modal-content {
    .con-box2 {
      margin-top: 36px;
      .tit {
        span {
          font-size: var(--font-size-module-title); // 使用模块标题变量 30px
          font-family: 'Microsoft YaHei';
          color: #fff;
        }
        span:nth-child(1) {
          display: inline-block;
          width: 43px;
          height: 22px;
          background: url('@/assets/home/<USER>') no-repeat center;
          background-size: 100% 100%;
          margin-right: 12px;
        }
      }
    }
    .con-box3 {
      margin-top: 36px;
      .tit {
        span {
          font-size: var(--font-size-module-title); // 使用模块标题变量 30px
          font-family: 'Microsoft YaHei';
          color: #fff;
        }
        span:nth-child(1) {
          display: inline-block;
          width: 43px;
          height: 22px;
          background: url('@/assets/home/<USER>') no-repeat center;
          background-size: 100% 100%;
          margin-right: 12px;
        }
      }
    }
    .con-box2,
    .con-box3 {
      .name {
        background: #262c3f;
        width: 100%;
        height: 62px;
        display: flex;
        color: #fff;
        margin-top: 20px;
        span {
          font-size: var(--font-size-content); // 使用统一变量 25px
          font-weight: bold;
          font-family: 'Microsoft YaHei';
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 20%;
        }
      }
      .list {
        width: 100%;
        height: 200px;
        margin-top: 10px;
        overflow-y: scroll;
        .line {
          display: flex;
          width: 100%;
          height: 62px;

          .line-span {
            color: #fff;
            font-size: var(--font-size-content); // 使用统一变量 25px
            font-family: 'Microsoft YaHei';
            height: 100%;
            width: 20%;
            line-height: 62px;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
      /* 隐藏垂直滚动条 */
      .list::-webkit-scrollbar {
        width: 0;
      }
    }
  }
}
.PopUp4 {
  width: 60% !important;
  .modal-content {
    .con-box2 {
      margin-top: 36px;
      .tit {
        span {
          font-size: var(--font-size-module-title); // 使用模块标题变量 30px
          font-family: 'Microsoft YaHei';
          color: #fff;
        }
        span:nth-child(1) {
          display: inline-block;
          width: 43px;
          height: 22px;
          background: url('@/assets/home/<USER>') no-repeat center;
          background-size: 100% 100%;
          margin-right: 12px;
        }
      }
    }
    .con-box3 {
      margin-top: 36px;
      .tit {
        span {
          font-size: var(--font-size-module-title); // 使用模块标题变量 30px
          font-family: 'Microsoft YaHei';
          color: #fff;
        }
        span:nth-child(1) {
          display: inline-block;
          width: 43px;
          height: 22px;
          background: url('@/assets/home/<USER>') no-repeat center;
          background-size: 100% 100%;
          margin-right: 12px;
        }
      }
    }
    .con-box2,
    .con-box3 {
      .name {
        background: #262c3f;
        width: 100%;
        height: 62px;
        display: flex;
        color: #fff;
        margin-top: 20px;
        span {
          font-size: var(--font-size-content); // 使用统一变量 25px
          font-weight: bold;
          font-family: 'Microsoft YaHei';
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          width: 20%;
        }
      }
      .list {
        width: 100%;
        height: 200px;
        margin-top: 10px;
        overflow-y: scroll;
        .line {
          display: flex;
          width: 100%;
          height: 62px;

          .line-span {
            color: #fff;
            font-size: var(--font-size-content); // 使用统一变量 25px
            font-family: 'Microsoft YaHei';
            height: 100%;
            width: 20%;
            line-height: 62px;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
      /* 隐藏垂直滚动条 */
      .list::-webkit-scrollbar {
        width: 0;
      }
    }
  }
}
.PopUp5 {
  max-width: 400px !important;
  left: 64%;
  top: 16% !important;
  overflow: hidden;
  .modal-content {
    width: 100%;
    height: 100%;
    .line {
      width: 100%;
      height: 60px;
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 10px;
      .line-box {
        width: 50%;
        height: 100%;
        font-size: 25px;
        color: #fff;
        display: flex;
        align-items: center;
        .dot {
          width: 60px;
          height: 60px;
          margin-right: 10px;
        }
        .dot1 {
          background: url('@/assets/point/ais.png') no-repeat center;
          background-size: 100% 100%;
        }
        .dot2 {
          background: url('@/assets/point/ldsb.png') no-repeat center;
          background-size: 100% 100%;
        }
        .dot3 {
          background: url('@/assets/point/qx.png') no-repeat center;
          background-size: 100% 100%;
        }
        .dot4 {
          background: url('@/assets/point/sd.png') no-repeat center;
          background-size: 100% 100%;
        }
        .dot5 {
          background: url('@/assets/point/jk.png') no-repeat center;
          background-size: 100% 100%;
        }
        .dot6 {
          background: url('@/assets/point/kk.png') no-repeat center;
          background-size: 100% 100%;
        }
        .dot7 {
          background: url('@/assets/point/dqjk.png') no-repeat center;
          background-size: 100% 100%;
        }
      }
    }
  }
}
</style>
