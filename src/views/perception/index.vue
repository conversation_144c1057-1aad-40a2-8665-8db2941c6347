<template>
  <div class="perception">
    <div class="per-right">
      <div class="per-box1">
        <Box :title="'事故情况'" class="Box1 interactive-card" :onTitleClick="openModal1">
          <template v-slot:content>
            <div class="AccidentEcharts" ref="AccidentEchartsRef"></div>
          </template>
        </Box>
        <Box :title="'结构健康'" class="Box2 interactive-card" :onTitleClick="openModal3">
          <template v-slot:content>
            <div class="content">
              <div class="top-box">
                <div class="box">
                  <div class="tit">健康指标</div>
                  <div class="line">
                    <span>{{ healthData?.damagePercentage || '0%' }}</span>
                    <img :src="jkzbImg" alt="" />
                  </div>
                </div>
                <div class="box">
                  <div class="tit">损伤评估</div>
                  <div class="line">
                    <span>{{ healthData?.healthPercentage || '0%' }}</span>
                    <img :src="sspgImg" alt="" />
                  </div>
                </div>
                <div class="box">
                  <div class="tit">安全性评估</div>
                  <div class="line">
                    <span>{{ healthData?.damagePercentage || '0%' }}</span>
                    <img :src="aqxpgImg" alt="" />
                  </div>
                </div>
              </div>
              <div class="bot">
                <div class="th">
                  <span>桥梁名称</span>
                  <span>健康度</span>
                  <span>更新日期</span>
                </div>
                <div class="con">
                  <div class="td" v-for="(item, index) in healthData?.bridgeDetails" :key="index">
                    <span>{{ item.bridgeName }}</span>
                    <span>{{ item.healthLevel }}</span>
                    <span>{{ formatDate(item.updateTime) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </Box>
        <Box :title="'社会舆情'" class="Box3" :onTitleClick="openModal4">
          <template v-slot:unit>
            <div class="unit">
              <el-select v-model="value" placeholder="Select" @change="updateTime" size="large" style="width: 72px">
                <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </template>
          <template v-slot:content>
            <div class="sentiment" ref="SentimentEchartsRef"></div>
          </template>
        </Box>
      </div>
      <div class="per-box2">
        <Box :title="'拥堵对比'" class="Box1">
          <template v-slot:content>
            <div class="content">
              <div class="container" ref="jamRef">
                <div class="bg"></div>
              </div>
            </div>
          </template>
        </Box>
        <Box :title="'车流趋势'" class="Box2">
          <template v-slot:unit>
            <div class="unit">
              <el-select v-model="value2" placeholder="Select" @change="updateTime2" size="large" style="width: 72px">
                <el-option v-for="item in options2" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
          </template>
          <template v-slot:content>
            <div class="FlowEcharts" ref="FlowEchartsRef"></div>
          </template>
        </Box>
        <Box :title="'预警监督'" class="Box3" :onTitleClick="openVideoStructureModal">
          <template v-slot:content>
            <div class="content">
              <div class="list">
                <div class="list-box" v-for="(item, index) in incidentData.events" :key="index">
                  <div class="img">
                    <img v-if="item.imagePath" :src="item.imagePath" alt="" />
                    <img v-else-if="item.fileUrl && item.fileUrl[0]" :src="item.fileUrl[0]" alt="" />
                    <noData v-else text="暂无图片"></noData>
                  </div>
                  <div class="con">
                    <span>发生时间：{{ formatDate(item.alarmTime || item.publishTime) }}</span>
                    <span>发生地点：{{ item.location || '未知' }}</span>
                    <span>事件类型：{{ item.eventTypeId || item.title }}</span>
                  </div>
                  <div class="btn">
                    <div class="btn1"></div>
                    <div class="btn2" @click="addressClick(item)"></div>
                    <div class="btn3"></div>
                  </div>
                </div>
                <noData v-if="incidentData.events.length == 0"></noData>
              </div>
            </div>
          </template>
        </Box>
      </div>
    </div>
    <div class="per-bot">
      <Box :title="'网点视频监控'" class="Box1">
        <template v-slot:content>
          <div class="content">
            <div class="box1" ref="videoEchartsRef"></div>
            <div class="box2">
              <div class="img"></div>
              <span>2500</span>
              <span>摄像头总数</span>
            </div>
            <div class="box3">
              <div class="tit">
                <span>监控列表</span>
              </div>
              <div class="con">
                <div class="th">
                  <span>名称</span>
                  <span>地点</span>
                  <span>状态</span>
                </div>
                <div class="list">
                  <div class="td" v-for="(item, index) in videoData" :key="index">
                    <span>{{ item.name }}</span>
                    <span>{{ item.location }}</span>
                    <span>{{ item.status }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="box4">
              <div class="video">
                <button class="button left" @click="moveLeft" ref="leftButtonRef" :disabled="position === 0">←</button>
                <div class="slider" ref="sliderRef">
                  <VideoPlayer class="monitor" :showDirectionControl="false" :url="''" />
                  <!-- <easy-player
                    class="monitor"
                    :video-url="'ws://116.148.231.66:10000/proxy/sms/local/live/34020000001110000001_34020000001310000004.flv'"
                    autoplay
                    :live="true"
                  ></easy-player> -->

                  <!-- <div class="monitor">监控 1</div>
                  <div class="monitor">监控 2</div>
                  <div class="monitor">监控 3</div>
                  <div class="monitor">监控 4</div>
                  <div class="monitor">监控 5</div>
                  <div class="monitor">监控 6</div>
                  <div class="monitor">监控 7</div>
                  <div class="monitor">监控 8</div> -->
                </div>
                <button class="button right" @click="moveRight" ref="rightButtonRef">→</button>
              </div>
            </div>
          </div>
        </template>
      </Box>
    </div>
    <!-- 事故情况弹窗 -->
    <PopUp :visible="showModal1" title="事故情况" @update:visible="closeModal1" class="PopUp1">
      <div class="modal-content">
        <div class="btn">
          <span v-for="item in activeName2" :key="item.value" :class="item.value == currentIndex2 ? 'current' : ''" @click="questList2(item.value)">{{
            item.name
          }}</span>
        </div>
        <Table :columns="columns" :fetchData="fetchData" :searchParams="searchParams1">
          <!-- 自定义列内容 -->
          <template #details="scope">
            <el-button type="primary" @click="viewDetails(scope.row)" style="width: 78px; height: 32px"
              ><el-icon><Document /></el-icon>详情</el-button
            >
          </template>
          <template #status="scope">
            <span>{{ scope.row.status == 1 ? '未处理' : scope.row.status == 2 ? '处理中' : '已处理' }}</span>
          </template>
        </Table>
      </div>
    </PopUp>
    <!-- 事故情况详情弹窗 -->
    <PopUp :visible="showModal2" title="详情" @update:visible="(val) => (showModal2 = val)" class="PopUp2" style="left: 24%; width: 1014px">
      <div class="modal-content">
        <div class="list">
          <div class="line">
            <span
              >事故ID：<i>{{ warningList.id || '' }}</i></span
            >
            <span
              >事故名称：<i>{{ warningList.eventName || '' }}</i></span
            >
          </div>
          <div class="line">
            <span
              >桥梁名称：<i>{{ warningList.bridgeName || '' }}</i></span
            ><span
              >发生地点：<i>{{ warningList.location || '' }}</i></span
            >
          </div>
          <div class="line">
            <span
              >事故类型：<i>{{ warningList.eventCategoryName || '' }}</i></span
            ><span
              >事故时间：<i>{{ formatDate(warningList.happenTime) || '' }}</i></span
            >
          </div>
          <div class="line">
            <span
              >事故等级：<i>{{ warningList.eventLevelName || '' }}</i></span
            >
            <span
              >经纬度：<i>{{ warningList.lon || '' }},{{ warningList.lat || '' }}</i></span
            >
          </div>
          <div class="line">
            <span class="line-con"
              >事故内容：<i>{{ warningList.description || '' }}</i></span
            >
          </div>
        </div>
        <div class="video">
          <img class="video-box" v-if="warningList.fileUrl[0]" :src="warningList.fileUrl[0]" alt="" />
          <noData class="video-box" v-else text="暂无图片"></noData>
        </div>
      </div>
    </PopUp>
    <!-- 结构健康弹窗 -->
    <PopUp :visible="showModal3" title="结构健康" @update:visible="closeModal3" class="PopUp3">
      <div class="modal-content">
        <Table :columns="columns2" :fetchData="healthListFc"> </Table>
      </div>
    </PopUp>
    <!-- 社会舆情弹窗 -->
    <PopUp :visible="showModal4" title="社会舆情" @update:visible="closeModal4" class="PopUp4">
      <div class="modal-content">
        <!-- 搜索框 -->
        <div class="search-bar">
          <span>名称：</span>
          <el-input v-model="title" @change="updateTitle" placeholder="请输入" clearable style="width: 300px" />
          <el-button type="primary" @click="handleSearch" style="width: 100px; margin-left: 27px"
            ><el-icon><Search /></el-icon>搜索</el-button
          >
        </div>
        <Table :columns="columns3" :fetchData="sentimentList" :searchParams="searchParams">
          <!-- 自定义列内容 -->
          <template #index="scope">
            <span>{{ scope.index + 1 }}</span>
          </template>
          <template #details="scope">
            <el-button type="primary" @click="viewDetails2(scope.row)" style="width: 78px; height: 32px"
              ><el-icon><Document /></el-icon>详情</el-button
            >
          </template>
        </Table>
      </div>
    </PopUp>
    <!-- 社会舆情详情弹窗 -->
    <PopUp :visible="showModal5" title="详情" @update:visible="(val) => (showModal5 = val)" class="PopUp5" style="left: 24%; width: 1125px">
      <div class="modal-content">
        <div class="list">
          <div class="line">
            <span
              >标题：<i>{{ sentimentData.title || '' }}</i></span
            ><span
              >来源：<i>{{ sentimentData.sourceName || '' }}</i></span
            >
          </div>
          <div class="line">
            <span>发布人：<i>{{}}</i></span
            ><span
              >发布时间：<i>{{ formatDate(sentimentData.publishTime) || '' }}</i></span
            >
          </div>
          <div class="line">
            <span
              >内容：<i>{{ sentimentData.content || '' }}</i></span
            ><span
              >创建时间：<i>{{ formatDate(sentimentData.createTime) }}</i></span
            >
          </div>
          <div class="line">
            <span
              >链接：<i>{{ sentimentData.sourceUrl || '' }}</i></span
            ><span
              >修改时间：<i>{{ formatDate(sentimentData.updateTime) || '' }}</i></span
            >
          </div>
        </div>
      </div>
    </PopUp>
    <!-- 预警监督弹窗 -->
    <PopUp :visible="showModal6" title="预警监督" @update:visible="closeModal6" class="PopUp6">
      <div class="modal-content">
        <!-- 搜索框 -->
        <div class="btn">
          <span
            v-for="(item, index) in activeName"
            :key="index"
            :class="index == currentIndex ? 'current' : ''"
            @click="questList(index, item.value)"
            >{{ item.name }}</span
          >
        </div>
        <Table :columns="columns4" :fetchData="incidentFc" :searchParams="searchParams2">
          <!-- 自定义列内容 -->
          <template #details="scope">
            <el-button type="primary" @click="viewDetails3(scope.row)" style="width: 78px; height: 32px"
              ><el-icon><Document /></el-icon>详情</el-button
            >
          </template>
        </Table>
      </div>
    </PopUp>
    <!-- 预警监督详情弹窗 -->
    <PopUp :visible="showModal7" title="详情" @update:visible="(val) => (showModal7 = val)" class="PopUp7" style="left: 24%; width: 1014px">
      <div class="modal-content">
        <div class="list">
          <div class="line">
            <span
              >预警类型：<i>{{ incidentList.title || '' }}</i></span
            ><span
              >预警ID：<i>{{ incidentList.id || '' }}</i></span
            >
          </div>
          <div class="line">
            <span
              >桥梁名称：<i>{{ incidentList.bridgeName || '' }}</i></span
            ><span
              >发布地点：<i>{{ incidentList.location || '' }}</i></span
            >
          </div>
          <div class="line">
            <span
              >经纬度：<i>{{ incidentList.lon }},{{ incidentList.lat }}</i></span
            >
            <span
              >预警时间：<i>{{ formatDate(incidentList.publishTime) || '' }}</i></span
            >
          </div>
          <div class="line">
            <span
              >预警内容：<i>{{ incidentList.content || '' }}</i></span
            >
            <span></span>
          </div>
        </div>
        <div class="video">
          <img class="video-box" v-if="incidentList.fileUrl[0]" :src="incidentList.fileUrl[0]" alt="" />
          <noData class="video-box" v-else text="暂无图片"></noData>
        </div>
      </div>
    </PopUp>
    <!-- 视频结构详情弹窗 -->
    <PopUp :visible="showVideoStructureModal" title="预警监督" @update:visible="closeVideoStructureModal" class="PopUp8" style="width: 3500px; ">
      <div class="modal-content">
        <Table :columns="videoStructureColumns" :fetchData="videoStructureList">
          <!-- 自定义列内容 -->
          <template #image="scope">
            <img v-if="scope.row.imagePath" :src="scope.row.imagePath" alt="事件图片" style="width: 80px; height: 60px; object-fit: cover; border-radius: 4px;" />
            <span v-else style="color: #999;">暂无图片</span>
          </template>
          <template #watch="scope">
            <el-button type="primary" @click="watchVideo(scope.row)" style="width: 78px; height: 32px">
              观看
            </el-button>
          </template>
        </Table>
      </div>
    </PopUp>

  </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import { ref, onMounted, onBeforeUnmount } from 'vue';
import Box from '@/components/Box/index.vue';
import jkzbImg from '@/assets/perception/zkzb.png';
import sspgImg from '@/assets/perception/sspg.png';
import aqxpgImg from '@/assets/perception/aqxpg.png';
import PopUp from '@/components/PopUp/index.vue';
import Table from '@/components/Table/index.vue';
import {
  getEventStatistics,
  getEventList,
  getHealthstatistics,
  getHealthStatisticsPage,
  getSentimentStats,
  getSearchPage,
  getWarningPage,
  getFlowTrend
} from '@/api/bridge/perception';
import { getTrafficFlowStats, getDirectionFlowStats } from '@/api/bridge/trafficFlow';
import { getUrlVideo } from '@/api/bridge/point';
import VideoPlayer from '@/components/VideoPlayer/index.vue';
import noData from '@/components/noData/index.vue';
import { setViewToCoordinates, getPointById, updatePointCoordinates, addPoint, clearTypeFeatures } from '@/utils/mapMethods';
import policeImg from '@/assets/conduct/police.png';
import { getSpecialEvents, getBridgeWarning } from '@/api/bridge/forewarning';
import { ElMessage } from 'element-plus';
import { formatDate } from '@/utils/index';

// 图表引用
const FlowEchartsRef = ref<HTMLElement | null>(null);
const AccidentEchartsRef = ref<HTMLElement | null>(null);
const SentimentEchartsRef = ref<HTMLElement | null>(null);
const jamRef = ref<HTMLElement | null>(null);
const videoEchartsRef = ref<HTMLElement | null>(null);
// 图表实例
let FlowEcharts: echarts.ECharts | null = null;
let AccidentEcharts: echarts.ECharts | null = null;
let SentimentEcharts: echarts.ECharts | null = null;
let jamEcharts: echarts.ECharts | null = null;
let videoEcharts: echarts.ECharts | null = null;

// 表格列配置
const columns = ref([
  {
    'prop': 'id',
    'label': '事件ID'
  },
  {
    'prop': 'eventCategoryName',
    'label': '事件类型'
  },
  {
    'prop': 'eventLevelName',
    'label': '事件等级'
  },
  {
    'prop': 'bridgeName',
    'label': '桥梁名称'
  },
  {
    'prop': 'status',
    'label': '处置状态',
    'slot': 'status'
  },
  {
    'prop': 'happenTime',
    'label': '发生时间',
    'formatter': (row: any) => formatDate(row.happenTime)
  },
  {
    'prop': 'details',
    'label': '操作',
    'slot': 'details' // 使用插槽自定义操作按钮（如“详情”链接）
  }
]);

//事故情况列表
const fetchData = async (params: {}) => {
  try {
    const res = await getEventList(params);
    if (res.code === 200) {
      return {
        data: res.rows || [],
        total: res.total || 0
      };
    }
  } catch (error) {
    console.error('获取事故情况数据失败:', error);
    ElMessage.error('获取数据失败');
    return {
      data: [],
      total: 0
    };
  }
};
const activeName2 = ref([
  {
    name: '未处理',
    value: 1
  },
  {
    name: '处理中',
    value: 2
  },
  {
    name: '已处理',
    value: 3
  }
]);
const currentIndex2 = ref(1);
//条件查询
const questList2 = (status) => {
  currentIndex2.value = status;
  searchParams1.value.status = status;
};
const searchParams1 = ref({
  status: 1
});

// 事故情况弹窗状态
const showModal1 = ref(false);
const showModal2 = ref(false);

// 弹窗管理函数
const openModal1 = () => {
  showModal1.value = true;
};

const closeModal1 = (val: boolean) => {
  showModal1.value = val;
};
//根据事件id查询详情
const warningList = ref<any>({});
const viewDetails = async (item) => {
  showModal2.value = true;
  warningList.value = {
    ...item,
    fileUrl: JSON.parse(item.fileUrl)
  };
};

const value = ref(3);
const options = [
  {
    value: 1,
    label: '日'
  },
  {
    value: 2,
    label: '月'
  },
  {
    value: 3,
    label: '年'
  }
];

//网点监控列表
const videoData = ref([
  {
    name: '监控 1',
    location: '地点 1',
    status: '状态 1'
  },
  {
    name: '监控 2',
    location: '地点 2',
    status: '状态 2'
  },
  {
    name: '监控 3',
    location: '地点 3',
    status: '状态 3'
  },
  {
    name: '监控 4',
    location: '地点 4',
    status: '状态 4'
  },
  {
    name: '监控 5',
    location: '地点 5',
    status: '状态 5'
  },
  {
    name: '监控 6',
    location: '地点 6',
    status: '状态 6'
  },
  {
    name: '监控 7',
    location: '地点 7',
    status: '状态 7'
  },
  {
    name: '监控 8',
    location: '地点 8',
    status: '状态 8'
  }
]);

//轮播
const position = ref(0);
const itemWidth = 400; // 每个监控项的宽度（包括 margin）
const visibleItems = 5; // 一次显示的监控项数量
const sliderRef = ref<HTMLElement | null>(null);
const leftButtonRef = ref<HTMLElement | null>(null);
const rightButtonRef = ref<HTMLElement | null>(null);
const moveLeft = () => {
  if (position.value > 0) {
    position.value--;
    updateSliderPosition();
    rightButtonRef.value!.disabled = false;
  }
  if (position.value === 0) {
    leftButtonRef.value!.disabled = true;
  } else {
    leftButtonRef.value!.disabled = false;
  }
};
const moveRight = () => {
  const maxPosition = Math.ceil(sliderRef.value!.children.length / visibleItems) - 1;
  if (position.value < maxPosition) {
    position.value++;
    updateSliderPosition();
    leftButtonRef.value!.disabled = false;
  }
  if (position.value === maxPosition) {
    rightButtonRef.value!.disabled = true;
  } else {
    rightButtonRef.value!.disabled = false;
  }
};
const updateSliderPosition = () => {
  sliderRef.value!.style.transform = `translateX(-${position.value * itemWidth * visibleItems}px)`;
};

//获取结构健康数据
const healthData = ref<any>({});
const healthFc = async () => {
  const res = await getHealthstatistics({});
  if (res.code === 200) {
    // console.log(res);
    healthData.value = res.data || {};
  }
};

//结构健康弹窗状态
const showModal3 = ref(false);

// 结构健康弹窗管理函数
const openModal3 = () => {
  showModal3.value = true;
};

const closeModal3 = (val: boolean) => {
  showModal3.value = val;
};
//结构健康表格列配置
const columns2 = ref([
  {
    'prop': 'bridgeUniqueCode',
    'label': '桥梁编号',
    'align': 'center'
  },
  {
    'prop': 'bridgeName',
    'label': '桥梁名称',
    'align': 'center'
  },
  {
    'prop': 'healthLevel',
    'label': '健康评分',
    'align': 'center',
    'sortable': true // 支持排序
  },
  {
    'prop': 'issues',
    'label': '问题',
    'align': 'center',
    'show-overflow-tooltip': true // 内容过长时显示提示框
  },
  {
    'prop': 'monitoringUnit',
    'label': '监测单位',
    'align': 'center',
    'show-overflow-tooltip': true
  },
  {
    'prop': 'updateTime',
    'label': '监测时间',
    'align': 'center',
    'sortable': true, // 支持按时间排序
    'formatter': (row: any) => formatDate(row.updateTime)
  }
]);
//结构健康表格数据
//结构健康表格获取数据
const healthListFc = async (params: {}) => {
  try {
    const res = await getHealthStatisticsPage(params);
    // console.log(res);
    if (res.code === 200) {
      return {
        data: res.rows || [],
        total: res.total || 0
      };
    }
  } catch (error) {
    console.error('获取结构健康数据失败:', error);
    ElMessage.error('获取数据失败');
    return {
      data: [],
      total: 0
    };
  }
};

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  FlowEcharts?.resize();
  AccidentEcharts?.resize();
  SentimentEcharts?.resize();
  jamEcharts?.resize();
  videoEcharts?.resize();
};

//事故情况图表
const initAccidentEcharts = async () => {
  const res = await getEventStatistics({});
  let totalEventCount = 0;
  let dataList = [];
  if (res.code === 200) {
    totalEventCount = res.data.totalEventCount || 0;
    res.data.bridgeList.forEach((item) => {
      dataList.push({
        name: item.bridgeName,
        value: item.eventCount || 0
      });
    });
    // console.log(res);
  }
  if (AccidentEchartsRef.value) {
    AccidentEcharts = echarts.init(AccidentEchartsRef.value);
    const option = {
      legend: {
        right: '5%',
        orient: 'vertical',
        y: 'center',
        // 自定义图例文本样式
        textStyle: {
          fontSize: 17, // 字体大小
          color: '#fff', // 字体颜色
          // fontWeight: 'bold', // 字体粗细
          fontFamily: 'Microsoft YaHei' // 字体类型
        },
        itemGap: 30,
        formatter: function (name) {
          // 找到对应的数据项
          const dataItem = option.series[0].data.find((item) => item.name === name);
          if (dataItem) {
            // 计算百分比
            const percent = ((dataItem.value / totalEventCount) * 100).toFixed(2);
            return `${name}         ${percent}%`;
          }
          return name;
        }
      },
      series: [
        {
          name: 'Nightingale Chart',
          type: 'pie',
          radius: [80, 180],
          center: ['30%', '50%'],
          roseType: 'area',
          itemStyle: {
            borderRadius: 8
          },
          label: {
            show: false // 设置为 false 以完全不显示标签文本
          },
          labelLine: {
            show: false // 设置为 false 以去除引导线
          },
          data: dataList
        }
      ]
    };
    AccidentEcharts.setOption(option);
  }
};

//社会舆情图表
const initSentimentEcharts = async (params) => {
  const res = await getSentimentStats({
    timeGranularity: params
  });
  let xData = []; //x轴数据
  let positiveData = []; //正面
  let negativeData = []; //负面
  let neutralData = []; //中性
  if (res.code === 200) {
    // console.log(res);
    xData = res.data.dates || [];
    positiveData = res.data.positiveCounts || [];
    negativeData = res.data.negativeCounts || [];
    neutralData = res.data.neutralCounts || [];
  }
  if (SentimentEchartsRef.value) {
    SentimentEcharts = echarts.init(SentimentEchartsRef.value);
    const option = {
      xAxis: {
        type: 'category',
        data: xData ? xData : ['00:00', '03:00', '06:00', '09:00', '12:00', '15:00', '18:00', '21:00'],
        axisLabel: {
          color: '#86868d',
          fontSize: 20
        },
        axisLine: {
          lineStyle: {
            color: '#86868d'
          }
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#86868d',
          fontSize: 20
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#86868d'
          }
        },
        splitLine: {
          show: false // 不显示 Y 轴的网格线
        }
      },
      series: [
        {
          name: '正面',
          data: positiveData ? positiveData : [300, 200, 600, 800, 400, 300, 330, 200],
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#00fffc',
            width: 2
          },
          itemStyle: {
            color: '#00fffc'
          }
        },
        {
          name: '负面',
          data: negativeData ? negativeData : [300, 200, 600, 800, 400, 300, 330, 200],
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#b10001',
            width: 2
          },
          itemStyle: {
            color: '#b10001'
          }
        },
        {
          name: '中性',
          data: neutralData ? neutralData : [600, 500, 400, 300, 200, 100, 300, 200],
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#fff700',
            width: 2
          },
          itemStyle: {
            color: '#fff700'
          }
        }
      ],
      legend: {
        right: 60,
        data: ['正面', '负面', '中性'],
        textStyle: {
          color: '#fff',
          fontSize: 20
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#23334e', // 背景色
        textStyle: {
          color: '#fff' // 字体颜色
        }
      },
      grid: {
        top: 40,
        right: 0,
        bottom: 0,
        left: 0,
        containLabel: true,
        show: true,
        borderWidth: 0 // 不显示外边框
      }
    };
    SentimentEcharts.setOption(option);
  }
};
const updateTime = (value) => {
  initSentimentEcharts(value);
};

//社会舆情弹窗
const showModal4 = ref(false);
const showModal5 = ref(false);

// 社会舆情弹窗管理函数
const openModal4 = () => {
  console.log('Opening modal4');
  showModal4.value = true;
};

const closeModal4 = (val: boolean) => {
  console.log('Closing modal4, value:', val);
  showModal4.value = val;
};
// 搜索条件
const title = ref('');
const searchParams = ref({
  title: ''
});
const updateTitle = (value) => {
  title.value = value;
};
// 搜索按钮点击事件
const handleSearch = () => {
  searchParams.value.title = title.value;
};
const columns3 = ref([
  {
    'label': '序号',
    'prop': 'index',
    'slot': 'index',
    'width': 80
  },
  {
    'label': '标题',
    'prop': 'title'
  },
  {
    'label': '来源',
    'prop': 'sourceName'
  },
  {
    'label': '发布时间',
    'prop': 'publishTime',
    'formatter': (row: any) => formatDate(row.publishTime)
  },
  {
    'prop': 'details',
    'label': '操作',
    'slot': 'details',
    'width': 120
  }
]);
//获取社会舆情列表数据
const sentimentList = async (params: {}) => {
  try {
    const res = await getSearchPage(params);
    if (res.code == 200) {
      // console.log(res);
      return {
        data: res.data.records || [],
        total: res.data.total || 0
      };
    }
  } catch (error) {
    console.error('获取社会舆情数据失败:', error);
    ElMessage.error('获取数据失败');
    return {
      data: [],
      total: 0
    };
  }
};
//查看详情
const viewDetails2 = (item) => {
  showModal5.value = true;
  sentimentData.value = item;
  // console.log('查看详情', item);
};
//详情数据
const sentimentData = ref<any>({});

//拥堵对比图表 - 使用新的方向流量统计接口
const initJamEcharts = async () => {
  let inValue = 0; // 进的数据，从接口获取
  let outValue = 0; // 出的数据，从接口获取
  let totalCount = 0; // 总车次，从接口获取

  try {
    // 获取方向流量统计数据
    const res = await getDirectionFlowStats();
    if (res.code === 200) {
      const data = res.data;
      totalCount = data.totalCount;

      // 计算舟向和宁向的百分比
      const zhouPercent = parseFloat(data.舟向占比.replace('%', ''));
      const ningPercent = parseFloat(data.非舟向占比.replace('%', ''));

      inValue = ningPercent; // 宁向作为进
      outValue = zhouPercent; // 舟向作为出
    } else {
      console.error('方向流量统计接口返回错误:', res.msg);
      return; // 如果接口失败，不渲染图表
    }
  } catch (error) {
    console.error('获取方向流量数据失败:', error);
    return; // 如果接口失败，不渲染图表
  }

  if (jamRef.value) {
    jamEcharts = echarts.init(jamRef.value);

    // 动态颜色分段
    const colorStops = [
      [inValue / 100, '#6fb7ce'], // 进部分
      [1, '#5c80f2'] // 出部分
    ];
    const option = {
      series: [
        {
          type: 'gauge', // 使用仪表盘类型
          center: ['50%', '100%'], // 中心点位置
          radius: '120%', // 半径
          startAngle: 180, // 起始角度
          endAngle: 0, // 结束角度
          min: 0,
          max: 100,
          splitNumber: 10,
          axisLine: {
            lineStyle: {
              width: 20, // 进度条宽度
              color: colorStops // 动态颜色分段
            }
          },
          axisTick: {
            show: false // 隐藏刻度
          },
          splitLine: {
            show: false // 隐藏分割线
          },
          axisLabel: {
            show: false // 隐藏标签
          },
          pointer: {
            show: false // 隐藏指针
          },
          detail: {
            show: false // 隐藏详情
          },
          data: [
            {
              value: inValue // 当前值
            }
          ]
        }
      ],
      title: {
        text: `${totalCount}\n车次`, // 显示实际车次数据
        left: 'center',
        bottom: '10%',
        textStyle: {
          fontSize: 30,
          fontWeight: 'bold',
          color: '#fff',
          fontFamily: 'Microsoft YaHei'
        }
      },
      graphic: [
        {
          type: 'text',
          left: '10%', // 左侧位置
          top: '30%', // 顶部位置
          style: {
            text: `进 ${inValue}%`, // 显示进的百分比
            fill: '#fff',
            fontWeight: 'bold',
            fontSize: 30,
            fontFamily: 'Microsoft YaHei'
          }
        },
        {
          type: 'text',
          right: '10%', // 右侧位置
          top: '30%', // 顶部位置
          style: {
            text: `出 ${outValue}%`, // 显示出的百分比
            fill: '#fff',
            fontWeight: 'bold',
            fontSize: 30,
            fontFamily: 'Microsoft YaHei'
          }
        }
      ]
    };
    jamEcharts.setOption(option);
  }
};

//车流趋势图表
const initFlowEcharts = async () => {
  const xData = [];
  const inData = [];
  const outData = [];
  const historyData = [];

  try {
    // 使用新的流量统计接口
    const res: any = await getTrafficFlowStats(value2.value);
    if (res.code === 200) {
      Object.keys(res.data).forEach((key) => {
        xData.push(key);
        inData.push(res.data[key].value1);
        outData.push(res.data[key].value2);
        historyData.push(res.data[key].value3);
      });
    } else {
      console.error('流量统计接口返回错误:', res.msg);
    }
  } catch (error) {
    console.error('获取流量趋势数据失败:', error);
  }

  if (FlowEchartsRef.value) {
    FlowEcharts = echarts.init(FlowEchartsRef.value);
    const option = {
      xAxis: {
        type: 'category',
        data: xData,
        axisLabel: {
          color: '#86868d',
          fontSize: 20
        },
        axisLine: {
          lineStyle: {
            color: '#86868d'
          }
        },
        splitLine: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#86868d',
          fontSize: 20
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#86868d'
          }
        },
        splitLine: {
          show: false // 不显示 Y 轴的网格线
        }
      },
      series: [
        {
          name: '进',
          data: inData,
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#a6d8fc',
            width: 2
          },
          areaStyle: {
            color: 'rgba(168, 214, 253, 0.2)' // 设置面积颜色
          },
          itemStyle: {
            color: '#a6d8fc'
          }
        },
        {
          name: '出',
          data: outData,
          type: 'line',
          smooth: true,
          areaStyle: {
            color: 'rgba(42, 191, 174, 0.2)' // 设置面积颜色
          },
          lineStyle: {
            color: '#35bfb0',
            width: 2
          },
          itemStyle: {
            color: '#35bfb0'
          }
        },
        {
          name: '历史',
          data: historyData,
          type: 'line',
          smooth: true,
          areaStyle: {
            color: 'rgba(96, 129, 237, 0.2)' // 设置面积颜色
          },
          lineStyle: {
            color: '#6081ed',
            width: 2
          },
          itemStyle: {
            color: '#6081ed'
          }
        }
      ],
      legend: {
        right: 60,
        data: ['进', '出', '历史'],
        textStyle: {
          color: '#fff',
          fontSize: 20
        }
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: '#23334e', // 背景色
        textStyle: {
          color: '#fff' // 字体颜色
        }
      },
      grid: {
        top: 40,
        right: 0,
        bottom: 0,
        left: 0,
        containLabel: true,
        show: true,
        borderWidth: 0 // 不显示外边框
      }
    };
    FlowEcharts.setOption(option);
  }
};
const updateTime2 = (value) => {
  // value2.value = value;
  initFlowEcharts();
};
const value2 = ref(1);
const options2 = [
  {
    value: 1,
    label: '日'
  },
  {
    value: 2,
    label: '月'
  },
  {
    value: 3,
    label: '年'
  }
];

//预警监督数据
const incidentData = ref<any>({
  events: [],
  statusStats: {},
  totalCount: 0
});
//获取预警监督数据
const getSpecialEventsFc = async () => {
  const res = await getSpecialEvents({ pageNum: 1, pageSize: 10 });
  if (res.code == 200) {
    console.log('getSpecialEventsFc response:', res);
    // 适配新的数据结构，可能是 res.data 直接是数组，也可能是 res.data.events
    const events = res.data.records || res.data || [];
    incidentData.value.events = events.map((item) => {
      const pointId = `point_warning${item.eventId || item.id}`;
      // 新数据结构中 imagePath 直接是字符串，不需要 JSON.parse
      const fileUrl = item.imagePath ? [item.imagePath] : (item.fileUrl ? JSON.parse(item.fileUrl) : []);
      // 弹窗内容
      const popupContent = `
          <div class="popup-title" style="min-width:700px;min-height:60px;display:flex;justify-content: space-between;align-items: center;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:0px 25px;">
        <span style="color:#fff;font-size:var(--font-size-data-large);font-weight: bold;">${item.eventTypeId || item.title}</span><button class="popup-close" style="font-size:var(--font-size-content)">X</button>
      </div>
      <div class="popup-content" style="min-width:700px;min-height:200px;background: linear-gradient(141deg, rgba(40, 40, 61, 0.85) 0%, rgba(0, 0, 0, 0.57) 44%, rgba(4, 3, 9, 0) 100%);padding:25px;border-top: 1px solid #accbff; ">
        <div class="ship-popup" style="width:100%;height:100%;">
              <div class="ship-info" style="width:100%;font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">事件类型: ${item.eventTypeId || item.title || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">事件ID: ${item.eventId || item.id || ''}</span>
              </div>
              <div class="ship-info" style="width:100%;font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">设备编号: ${item.deviceId || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">车道: ${item.lane || ''}</span>
              </div>
              <div class="ship-info" style="width:100%;font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">经纬度: ${item.longitude || item.lon || ''},${item.latitude || item.lat || ''}</span>
                <span style="display:inline-block;width:50%;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">事件时间: ${formatDate(item.alarmTime || item.publishTime) || ''}</span>
              </div>
              <div class="ship-info" style="width:100%;font-size: var(--font-size-content);color: #fff;display:flex;justify-content: space-between;margin-bottom:20px;">
                <span style="display:inline-block;width:100%;overflow: hidden;text-overflow: ellipsis;white-space: wrap;">事件内容: ${item.content || ''}</span>
              </div>
               <div class="ship-info" style="font-size: var(--font-size-content);color: #fff;display:flex;margin-bottom:20px;">
                ${
                  fileUrl && fileUrl[0]
                    ? `<img style="width:300px;height:100px;object-fit:cover;" src="${fileUrl[0]}" alt="预警图片" />`
                    : `<div style="width:300px;height:100px;background:#2f3039;display:flex;justify-content:center;align-items:center;color:#fff;">暂无图片</div>`
                }
              </div>
            </div>
      </div>
          `;

      //判断有没有
      const existingPoint = getPointById(pointId);
      const longitude = item.longitude || item.lon || 0;
      const latitude = item.latitude || item.lat || 0;

      if (existingPoint) {
        // 更新点位坐标
        updatePointCoordinates(existingPoint, [longitude, latitude]);
      } else {
        // 添加新的点位
        if (longitude && latitude) {
          addPoint([longitude, latitude], policeImg, 1, popupContent, pointId);
        }
      }
      return {
        ...item,
        fileUrl
      };
    });
    incidentData.value.statusStats = res.data.statusStats;
    incidentData.value.totalCount = res.data.totalCount;
  }
};
//事件地址点击
const addressClick = (item) => {
  const longitude = item.longitude || item.lon;
  const latitude = item.latitude || item.lat;
  if (latitude && longitude) {
    setViewToCoordinates([longitude, latitude]);
  }
};
//根据事件状态更改颜色
const warningState = (state) => {
  if (state == '1') {
    return 'current0';
  } else if (state == '3') {
    return 'current1';
  } else {
    return 'current2';
  }
};
const showModal6 = ref(false);

// 预警监督弹窗管理函数
const openModal6 = () => {
  console.log('Opening modal6');
  showModal6.value = true;
};

const closeModal6 = (val: boolean) => {
  console.log('Closing modal6, value:', val);
  showModal6.value = val;
};
const columns4 = ref([
  {
    prop: 'bridgeName',
    label: '桥梁名称',
    align: 'center'
  },
  {
    prop: 'title',
    label: '预警类型',
    align: 'center'
  },
  {
    prop: 'location',
    label: '发生地点',
    align: 'center'
  },
  {
    prop: 'publishTime',
    label: '发生时间',
    align: 'center',
    formatter: (row: any) => formatDate(row.publishTime)
  },
  {
    'prop': 'details',
    'label': '操作',
    'slot': 'details', // 使用插槽自定义操作按钮（如"详情"链接）
    width: 120
  }
]);
const activeName = ref([
  {
    name: '未处理',
    value: 1
  },
  {
    name: '处理中',
    value: 2
  },
  {
    name: '已处理',
    value: 3
  }
]);
const currentIndex = ref(0);
//条件查询
const questList = (index, status) => {
  currentIndex.value = index;
  searchParams2.value.status = status;
};
const searchParams2 = ref({
  status: 1
});
//获取预警监督列表数据
const incidentFc = async (params: {}) => {
  try {
    const res = await getBridgeWarning(params);
    if (res.code == 200) {
      return {
        data: res.data.list || [],
        total: res.data.total || 0
      };
    }
  } catch (error) {
    console.error('获取预警监督数据失败:', error);
    ElMessage.error('获取数据失败');
    return {
      data: [],
      total: 0
    };
  }
};
const incidentList = ref<any>({});
const viewDetails3 = (item) => {
  showModal7.value = true;
  incidentList.value = {
    ...item,
    fileUrl: JSON.parse(item.fileUrl)
  };
  // console.log('查看详情', item);
};
//预警监督详情弹窗
const showModal7 = ref(false);

// 视频结构相关变量
const showVideoStructureModal = ref(false);

// 视频结构弹窗管理函数
const openVideoStructureModal = () => {
  showVideoStructureModal.value = true;
};

const closeVideoStructureModal = (val: boolean) => {
  showVideoStructureModal.value = val;
};



// 视频结构表格列定义
const videoStructureColumns = ref([
  {
    prop: 'alarmTime',
    label: '事件时间',
    align: 'center',
    formatter: (row: any) => formatDate(row.alarmTime)
  },
  // {
  //   prop: 'avgSpeed',
  //   label: '平均车速(km/h)',
  //   align: 'center'
  // },
  // {
  //   prop: 'carAmount',
  //   label: '拥堵车辆数',
  //   align: 'center'
  // },
  {
    prop: 'carType',
    label: '车辆类型',
    align: 'center',
    formatter: (row: any) => {
      const typeMap = {
        1: '货车',
        2: '客车',
        3: '小车',
        4: '危化品车',
        5: '施工车'
      };
      return typeMap[row.carType] || '其他';
    }
  },
  {
    prop: 'congestionGrade',
    label: '拥堵等级',
    align: 'center'
  },
  // {
  //   prop: 'content',
  //   label: '事件详情',
  //   align: 'center',
  //   width: 200
  // },
  {
    prop: 'dataSource',
    label: '数据来源',
    align: 'center'
  },
  {
    prop: 'deviceId',
    label: '设备编号',
    align: 'center'
  },
  {
    prop: 'direction',
    label: '方向',
    align: 'center',
    formatter: (row: any) => {
      return row.direction === 0 ? 'OFT' : 'LEG';
    }
  },
  {
    prop: 'eventTypeId',
    label: '事件类型代码',
    align: 'center'
  },
  {
    prop: 'lane',
    label: '车道',
    align: 'center'
  },
  {
    prop: 'latitude',
    label: '纬度',
    align: 'center'
  },
  {
    prop: 'longitude',
    label: '经度',
    align: 'center'
  },
  {
    prop: 'milestone',
    label: '桩号(米)',
    align: 'center'
  },
  {
    prop: 'speed',
    label: '速度',
    align: 'center'
  },
  // {
  //   prop: 'vehiclePlate',
  //   label: '车牌号',
  //   align: 'center'
  // },
  // {
  //   prop: 'vehiclePlateColor',
  //   label: '车牌颜色',
  //   align: 'center'
  // },
  {
    prop: 'imagePath',
    label: '图片',
    slot: 'image',
    width: 120,
    align: 'center'
  },
  {
    prop: 'watch',
    label: '操作',
    slot: 'watch',
    width: 120,
    fixed: 'right'
  }
]);

// 获取视频结构列表数据
const videoStructureList = async (params: any) => {
  try {
    // 添加分页参数
    const requestParams = {
      ...params,
      pageNum: params.pageNum || 1,
      pageSize: params.pageSize || 10
    };

    const res = await getSpecialEvents(requestParams);
    if (res.code == 200) {
      return {
        data: res.data.records || [],
        total: res.data.total || 0
      };
    }
  } catch (error) {
    console.error('获取视频结构数据失败:', error);
    ElMessage.error('获取数据失败');
    return {
      data: [],
      total: 0
    };
  }
};

// 观看视频
const watchVideo = (item: any) => {
  try {
    // 使用后端返回的 videoPath 字段，直接在新网页中打开
    if (item.videoPath) {
      window.open(item.videoPath, '_blank');
    } else {
      ElMessage.warning('该项目暂无视频');
    }
  } catch (error) {
    console.error('打开视频失败:', error);
    ElMessage.error('打开视频失败');
  }
};

//视频监控图表
const initVideoEcharts = () => {
  if (videoEchartsRef.value) {
    videoEcharts = echarts.init(videoEchartsRef.value);

    const option = {
      title: {
        text: `2000\n正常运行\n数量`,
        x: 'center',
        y: 'center',
        textStyle: {
          fontWeight: 'bold',
          color: '#fff',
          fontSize: '25'
        }
      },
      color: ['#808080'],
      legend: {
        show: false,
        itemGap: 12,
        data: ['01', '02']
      },

      series: [
        {
          name: 'Line 1',
          type: 'pie',
          clockWise: true,
          radius: ['70%', '85%'],
          itemStyle: {
            normal: {
              label: {
                show: false
              },
              labelLine: {
                show: false
              }
            }
          },
          hoverAnimation: false,
          data: [
            {
              value: 2000,
              name: '01',
              itemStyle: {
                normal: {
                  color: {
                    // 完成的圆环的颜色
                    colorStops: [
                      {
                        offset: 0,
                        color: '#95f5ff' // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: '#95f5ff' // 100% 处的颜色
                      }
                    ]
                  },
                  label: {
                    show: false
                  },
                  labelLine: {
                    show: false
                  }
                }
              }
            },
            {
              name: '02',
              value: 500
            }
          ]
        }
      ]
    };
    videoEcharts.setOption(option);
  }
};

onMounted(() => {
  initFlowEcharts();
  initAccidentEcharts();
  initSentimentEcharts(3);
  initJamEcharts(); // 现在是async函数，会自动处理
  initVideoEcharts();
  healthFc();
  getSpecialEventsFc();
  window.addEventListener('resize', handleResize);
});

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
  // 销毁图表实例
  FlowEcharts?.dispose();
  FlowEcharts = null;
  videoEcharts?.dispose();
  videoEcharts = null;
  jamEcharts?.dispose();
  jamEcharts = null;
  AccidentEcharts?.dispose();
  AccidentEcharts = null;
  SentimentEcharts?.dispose();
  SentimentEcharts = null;
  clearTypeFeatures('point_warning'); //清除预警点位
});
</script>

<style lang="scss" scoped>
@import '@/assets/styles/variables.module.scss';
@import '@/assets/styles/mixin.scss';
.perception {
  .per-right {
    height: 1377px;
    width: 1442px;
    position: absolute;
    bottom: 44px;
    right: 60px;
    display: flex;
    justify-content: space-between;
    z-index: 1;
    .per-box1 {
      height: 100%;
      width: 48%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .Box1 {
        width: 100%;
        height: 446px;
        position: relative;
        .AccidentEcharts {
          flex: 1;
          width: 100%;
        }
      }
      .Box2 {
        width: 100%;
        height: 425px;
        position: relative;
        .content {
          flex: 1;
          width: 100%;
          overflow: hidden;
          .top-box {
            height: 116px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            .box {
              width: 32%;
              height: 100%;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              background: #262c38;
              padding: 13px 17px;
              .tit {
                color: #fff;
                font-size: var(--font-size-data-medium); // 使用统一变量 26px
                font-family: 'Microsoft YaHei';
              }
              .line {
                display: flex;
                align-items: center;
                justify-content: space-between;
                span {
                  color: #fff;
                  font-size: 35px; // 保持大数据显示
                  font-weight: bold;
                  font-family: 'Microsoft YaHei';
                }
                img {
                  width: 54px;
                  height: 54px;
                }
              }
            }
          }
          .bot {
            width: 100%;
            height: calc(100% - 136px);
            margin-top: 10px;
            .th {
              width: 100%;
              height: 50px;
              span {
                display: inline-block;
                width: 33%;
                height: 100%;
                text-align: center;
                line-height: 50px;
                color: #fff;
                font-size: var(--font-size-content); // 使用统一变量 25px
                font-family: 'Microsoft YaHei';
              }
            }
            .con {
              width: 100%;
              height: calc(100% - 50px);
              overflow-y: scroll;
              .td {
                width: 100%;
                height: 50px;
                span {
                  display: inline-block;
                  width: 33%;
                  height: 100%;
                  text-align: center;
                  line-height: 50px;
                  color: #fff;
                  font-size: var(--font-size-content); // 使用统一变量 25px
                  font-family: 'Microsoft YaHei';
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
              }
              .td:nth-child(2n + 1) {
                background-color: #262c38;
              }
            }
            /* 自定义滚动条宽度和高度 */
            .con::-webkit-scrollbar {
              width: 0px;
              height: 0px;
            }
          }
        }
      }
      .Box3 {
        width: 100%;
        height: 438px;
        position: relative;
        .unit {
          position: absolute;
          top: 24px;
          right: 0;
          width: 70%;
          height: 42px;
          display: flex;
          align-items: center;
          .el-select {
            background: #35373c;
            .el-select--large .el-select__wrapper {
              background: #35373c;
            }
            ::v-deep span {
              color: #fff;
              font-family: 'Microsoft YaHei';
              font-size: var(--font-size-content); // 使用统一变量 25px
              font-weight: bold;
            }
          }
        }
        .sentiment {
          flex: 1;
          width: 100%;
        }
      }
    }
    .per-box2 {
      height: 100%;
      width: 48%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .Box1 {
        width: 100%;
        height: 390px;
        position: relative;
        .content {
          flex: 1;
          width: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          .container {
            width: 494px;
            height: 242px;
            position: relative;
            background: url('@/assets/perception/yddbBg.png') no-repeat center;
            z-index: 1;
            .bg {
              width: 207px;
              height: 101px;
              position: absolute;
              bottom: 0;
              left: 50%;
              transform: translateX(-50%);
              background: url('@/assets/perception/yddbBg.png') no-repeat center;
              background-size: 100% 100%;
              z-index: -1;
            }
          }
        }
      }
      .Box2 {
        width: 100%;
        height: 377px;
        position: relative;
        .unit {
          position: absolute;
          top: 24px;
          right: 0;
          width: 70%;
          height: 42px;
          display: flex;
          align-items: center;
          .el-select {
            background: #35373c;
            .el-select--large .el-select__wrapper {
              background: #35373c;
            }
            ::v-deep span {
              color: #fff;
              font-family: 'Microsoft YaHei';
              font-size: var(--font-size-content); // 使用统一变量 25px
              font-weight: bold;
            }
          }
        }
        .FlowEcharts {
          flex: 1;
          width: 100%;
        }
      }
      .Box3 {
        width: 100%;
        height: 540px;
        position: relative;
        .content {
          flex: 1;
          overflow: hidden;
          .head {
            height: 118px;
            padding: 10px;
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            background: #0e0f15;
            border-radius: 4px;
            .head-lin {
              color: #fff;
              display: flex;
              justify-content: space-around;
              align-items: center;
              width: 48%;
              background: #222730;
              border-radius: 4px;
              .img1 {
                width: 16px;
                height: 16px;
                background: url('@/assets/forewarning/qb.png') no-repeat center;
                background-size: 100% 100%;
              }
              .img2 {
                width: 16px;
                height: 16px;
                background: url('@/assets/forewarning/czz.png') no-repeat center;
                background-size: 100% 100%;
              }
              .img3 {
                width: 16px;
                height: 16px;
                background: url('@/assets/forewarning/ycl.png') no-repeat center;
                background-size: 100% 100%;
              }
              .img4 {
                width: 16px;
                height: 16px;
                background: url('@/assets/forewarning/wcl.png') no-repeat center;
                background-size: 100% 100%;
              }
              span:nth-child(1) {
                margin-left: 20px;
              }
              span:nth-child(2) {
                font-size: var(--font-size-content); // 使用统一变量 25px
                font-family: 'Microsoft YaHei';
                flex: 1;
                margin-left: 20px;
              }
              span:nth-child(3) {
                font-size: var(--font-size-content); // 使用统一变量 25px
                font-family: 'Microsoft YaHei';
                font-weight: bold;
                margin-right: 40px;
              }
            }
          }
          .list {
            overflow-y: scroll;
            width: 100%;
            height: calc(100% - 128px);
            .list-box {
              width: 100%;
              height: 150px;
              display: flex;
              align-items: center;
              background: #1a1e23;
              margin-bottom: 20px;
              .img {
                width: 211px;
                height: 125px;
                margin-left: 10px;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
              .con {
                display: flex;
                height: 100%;
                flex: 1;
                flex-direction: column;
                justify-content: space-around;
                margin-left: 10px;
                span {
                  color: #fff;
                  display: inline-block;
                  font-size: 17px;
                  font-family: 'Microsoft YaHei';
                  i {
                    font-style: normal;
                    color: red;
                  }
                  .current0 {
                    color: red;
                  }
                  .current1 {
                    color: #00ff6d;
                  }
                  .current2 {
                    color: #efff00;
                  }
                }
              }
              .btn {
                width: 33px;
                height: 125px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                margin-right: 20px;
                > div {
                  width: 33px;
                  height: 33px;
                  border-radius: 4px;
                  cursor: pointer;
                }
                .btn1 {
                  background: #2a2b39 url('@/assets/forewarning/fxdj.png') no-repeat center;
                }
                .btn2 {
                  background: #2a2b39 url('@/assets/forewarning/dw.png') no-repeat center;
                }
                .btn3 {
                  background: #2a2b39 url('@/assets/forewarning/sxt.png') no-repeat center;
                }
              }
            }
          }
          /* 自定义滚动条宽度和高度 */
          ::-webkit-scrollbar {
            width: 0px;
            height: 0px;
          }
        }
      }
    }
  }
  .per-bot {
    height: 307px;
    width: 55%;
    position: absolute;
    bottom: 178px;
    left: 11.5%;
    z-index: 1;
    .Box1 {
      width: 100%;
      height: 307px;
      position: relative;
      .content {
        flex: 1;
        display: flex;
        overflow: hidden;
        .box1 {
          height: 100%;
          width: 290px;
          margin-left: 40px;
        }
        .box2 {
          height: 100%;
          width: 152px;
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-left: 50px;
          .img {
            width: 100%;
            height: 132px;
            background: url('@/assets/perception/jk.png') no-repeat center;
          }
          span {
            color: #fff;
            font-family: 'Microsoft YaHei';
          }
          span:nth-child(2) {
            font-size: 42px;
            font-weight: bold;
          }
          span:last-child(2) {
            font-size: 22px;
          }
        }
        .box3 {
          width: 600px;
          height: 100%;
          display: flex;
          flex-direction: column;
          margin-left: 100px;
          overflow: hidden;
          .tit {
            width: 100%;
            height: 30px;
            color: #fff;
            font-size: 22px;
            font-family: 'Microsoft YaHei';
            margin-bottom: 10px;
          }
          .con {
            width: 100%;
            height: calc(100% - 40px);
            overflow: hidden;
            .th {
              width: 100%;
              height: 30px;
              display: flex;
              justify-content: space-between;
              span {
                display: inline-block;
                width: 30%;
                height: 30px;
                color: #adecff;
                font-size: 18px;
                font-family: 'Microsoft YaHei';
                border-bottom: 1px solid #adecff;
              }
            }
            .list {
              width: 100%;
              height: calc(100% - 30px);
              overflow-y: scroll;
              .td {
                width: 100%;
                height: 30px;
                display: flex;
                justify-content: space-between;
                span {
                  display: inline-block;
                  width: 30%;
                  height: 30px;
                  color: #adecff;
                  font-size: 18px;
                  font-family: 'Microsoft YaHei';
                  border-bottom: 1px solid #adecff;
                }
              }
            }
          }
        }
        .box4 {
          width: 100%;
          height: 100%;
          margin-left: 100px;
          position: relative;
          overflow: hidden;
          .video {
            width: 100%;
            height: 100%;
            overflow: hidden;
            position: relative;
          }
          .slider {
            display: flex;
            transition: transform 0.5s ease-in-out;
          }
          .monitor {
            width: 400px;
            height: 219px;
            background-color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 10px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            font-size: 18px;
            font-weight: bold;
            color: #333;
          }
          .button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background-color: rgba(0, 0, 0, 0.5);
            color: #fff;
            border: none;
            padding: 10px;
            cursor: pointer;
            border-radius: 50%;
            font-size: 18px;
          }
          .button.left {
            left: 10px;
            z-index: 1;
          }
          .button.right {
            right: 10px;
            z-index: 1;
          }
          .button:disabled {
            background-color: rgba(0, 0, 0, 0.2);
            cursor: not-allowed;
          }
        }
      }
    }
  }
  .PopUp1 {
    .btn {
      height: 36px;
      width: 100%;
      display: flex;
      padding: 0 20px;
      span {
        display: inline-block;
        width: 165px;
        height: 100%;
        border-radius: 6px;
        color: #b6b6bb;
        font-size: 20px;
        font-family: 'Microsoft YaHei';
        background: #50505a;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
      }
      .current {
        background: #416194;
        color: #fff;
      }
    }
  }
  .PopUp2 {
    .modal-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      padding: 20px;
      .list {
        width: 100%;
        height: 310px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .line {
          width: 100%;
          height: 40px;
          span {
            display: inline-block;
            width: 50%;
            height: 100%;
            color: #fff;
            font-size: 30px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-family: 'Microsoft YaHei';
            i {
              font-style: normal;
              color: #accbff;
            }
          }
          .line-con {
            width: 100%;
            white-space: wrap;
          }
        }
      }
      .video {
        width: 100%;
        height: 258px;
        display: flex;
        justify-content: space-between;
        margin: 64px 0 0px 0;
        .video-box {
          width: 48%;
          height: 100%;
          // background: url('@/assets/secialVehicle/videoBg.png') no-repeat center;
          // background-size: 100% 100%;
        }
      }
    }
  }
  .PopUp4 {
    .search-bar {
      padding: 0 20px;
      width: 100%;
      display: flex;
      align-items: center;
      span {
        color: #fff;
        font-family: 'Microsoft YaHei';
        font-size: 24px;
      }
      :deep(.el-input__wrapper) {
        background: #474750;
      }
      :deep(.el-input__inner) {
        color: #fff;
        font-size: 25px;
      }
    }
  }
  .PopUp5 {
    .modal-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      padding: 20px;
      .list {
        width: 100%;
        height: 310px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .line {
          width: 100%;
          height: 40px;
          span {
            display: inline-block;
            width: 50%;
            height: 100%;
            color: #fff;
            font-size: 30px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-family: 'Microsoft YaHei';
            i {
              font-style: normal;
              color: #accbff;
            }
          }
        }
      }
    }
  }
  .PopUp6 {
    .btn {
      height: 36px;
      width: 100%;
      display: flex;
      padding: 0 20px;
      span {
        display: inline-block;
        width: 165px;
        height: 100%;
        border-radius: 6px;
        color: #b6b6bb;
        font-size: 20px;
        font-family: 'Microsoft YaHei';
        background: #50505a;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
      }
      .current {
        background: #416194;
        color: #fff;
      }
    }
  }
  .PopUp7 {
    .modal-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      padding: 20px;
      .list {
        width: 100%;
        height: 310px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .line {
          width: 100%;
          height: 40px;
          span {
            display: inline-block;
            width: 50%;
            height: 100%;
            color: #fff;
            font-size: 30px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-family: 'Microsoft YaHei';
            i {
              font-style: normal;
              color: #accbff;
            }
          }
        }
      }
      .video {
        width: 100%;
        height: 258px;
        display: flex;
        justify-content: space-between;
        margin: 64px 0 0px 0;
        .video-box {
          width: 48%;
          height: 100%;
          // background: url('@/assets/secialVehicle/videoBg.png') no-repeat center;
          // background-size: 100% 100%;
        }
      }
    }
  }



  // 视频结构弹窗样式
  .PopUp8 {
    .modal-content {
      width: 100%;
      padding: 20px;
    }
  }


}
</style>
